// 简单测试批量导入功能
// 使用方法：在项目根目录运行
// mvn compile exec:java -Dexec.mainClass="test_bulk_import" -Dexec.args="5"

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class test_bulk_import {
    public static void main(String[] args) {
        int days = 5; // 默认导入最近5天
        
        if (args.length > 0) {
            try {
                days = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                System.out.println("参数错误，使用默认值5天");
            }
        }
        
        System.out.println("测试NVD API连接，获取最近" + days + "天的CVE数据...");
        
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days);
            
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
            String pubStartDate = startDate.atStartOfDay().format(formatter);
            String pubEndDate = endDate.atTime(23, 59, 59).format(formatter);
            
            String urlStr = "https://services.nvd.nist.gov/rest/json/cves/2.0" +
                           "?resultsPerPage=10" +
                           "&startIndex=0" +
                           "&pubStartDate=" + pubStartDate +
                           "&pubEndDate=" + pubEndDate;
            
            System.out.println("请求URL: " + urlStr);
            
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(30000);
            conn.setReadTimeout(30000);
            
            int responseCode = conn.getResponseCode();
            System.out.println("响应码: " + responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                String jsonResponse = response.toString();
                System.out.println("响应长度: " + jsonResponse.length() + " 字符");
                
                // 简单解析JSON响应
                if (jsonResponse.contains("\"totalResults\"")) {
                    int totalStart = jsonResponse.indexOf("\"totalResults\":") + 15;
                    int totalEnd = jsonResponse.indexOf(",", totalStart);
                    if (totalEnd == -1) totalEnd = jsonResponse.indexOf("}", totalStart);
                    
                    String totalStr = jsonResponse.substring(totalStart, totalEnd);
                    System.out.println("总记录数: " + totalStr);
                }
                
                if (jsonResponse.contains("\"vulnerabilities\"")) {
                    int vulnStart = jsonResponse.indexOf("\"vulnerabilities\":[") + 19;
                    int vulnEnd = jsonResponse.indexOf("]", vulnStart);
                    String vulnArray = jsonResponse.substring(vulnStart, vulnEnd);
                    
                    // 计算CVE数量（简单方法）
                    int cveCount = 0;
                    int index = 0;
                    while ((index = vulnArray.indexOf("\"CVE-", index)) != -1) {
                        cveCount++;
                        index += 5;
                    }
                    
                    System.out.println("当前页CVE数量: " + cveCount);
                }
                
                System.out.println("API连接测试成功！");
                System.out.println("可以开始批量导入CVE数据。");
                
            } else {
                System.out.println("API请求失败，响应码: " + responseCode);
                
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
                StringBuilder errorResponse = new StringBuilder();
                String errorLine;
                
                while ((errorLine = errorReader.readLine()) != null) {
                    errorResponse.append(errorLine);
                }
                errorReader.close();
                
                System.out.println("错误响应: " + errorResponse.toString());
            }
            
        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
