-- 安全扫描系统数据库脚本
-- 基于RuoYi框架的安全扫描管理系统

-- 项目信息表
DROP TABLE IF EXISTS `project_info`;
CREATE TABLE `project_info` (
  `project_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) NOT NULL COMMENT '项目编码',
  `git_url` varchar(500) NOT NULL COMMENT 'GitLab地址',
  `git_branch` varchar(100) DEFAULT 'master' COMMENT 'Git分支',
  `git_username` varchar(100) COMMENT 'GitLab用户名',
  `git_password` varchar(200) COMMENT 'GitLab密码或Token(加密)',
  `project_desc` text COMMENT '项目描述',
  `project_owner` varchar(50) COMMENT '项目负责人',
  `scan_status` char(1) DEFAULT '0' COMMENT '扫描状态(0未扫描 1扫描中 2已完成 3扫描失败)',
  `last_scan_time` datetime COMMENT '最后扫描时间',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0代表存在 2代表删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`project_id`),
  UNIQUE KEY `uk_project_code` (`project_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='项目信息表';

-- 扫描任务表
DROP TABLE IF EXISTS `scan_task`;
CREATE TABLE `scan_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_status` char(1) DEFAULT '0' COMMENT '任务状态(0待执行 1执行中 2已完成 3执行失败)',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `total_dependencies` int(11) DEFAULT 0 COMMENT '总依赖数',
  `cve_high_count` int(11) DEFAULT 0 COMMENT '高危CVE数量',
  `cve_medium_count` int(11) DEFAULT 0 COMMENT '中危CVE数量',
  `cve_low_count` int(11) DEFAULT 0 COMMENT '低危CVE数量',
  `license_high_risk_count` int(11) DEFAULT 0 COMMENT '高风险许可证数量',
  `license_medium_risk_count` int(11) DEFAULT 0 COMMENT '中风险许可证数量',
  `error_msg` text COMMENT '错误信息',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`task_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='扫描任务表';

-- CVE漏洞信息表
DROP TABLE IF EXISTS `cve_info`;
CREATE TABLE `cve_info` (
  `cve_id` varchar(20) NOT NULL COMMENT 'CVE编号',
  `cve_desc` text COMMENT 'CVE描述',
  `cvss_score` decimal(3,1) COMMENT 'CVSS评分',
  `severity_level` char(1) COMMENT '严重等级(1低危 2中危 3高危 4严重)',
  `publish_date` date COMMENT '发布日期',
  `update_date` date COMMENT '更新日期',
  `affected_components` text COMMENT '影响组件(JSON格式)',
  `references` text COMMENT '参考链接(JSON格式)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`cve_id`)
) ENGINE=InnoDB COMMENT='CVE漏洞信息表';

-- 项目依赖表
DROP TABLE IF EXISTS `project_dependency`;
CREATE TABLE `project_dependency` (
  `dependency_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '依赖ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `task_id` bigint(20) NOT NULL COMMENT '扫描任务ID',
  `group_id` varchar(200) NOT NULL COMMENT 'Maven GroupId',
  `artifact_id` varchar(200) NOT NULL COMMENT 'Maven ArtifactId',
  `version` varchar(50) NOT NULL COMMENT '版本号',
  `license_type` varchar(100) COMMENT '许可证类型',
  `license_risk_level` char(1) COMMENT '许可证风险等级(1低风险 2中风险 3高风险)',
  `has_cve` char(1) DEFAULT '0' COMMENT '是否存在CVE(0否 1是)',
  `cve_count` int(11) DEFAULT 0 COMMENT 'CVE数量',
  `max_cvss_score` decimal(3,1) COMMENT '最高CVSS评分',
  `cve_details` text COMMENT 'CVE详情(JSON格式)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`dependency_id`),
  KEY `idx_project_task` (`project_id`, `task_id`),
  KEY `idx_group_artifact` (`group_id`, `artifact_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='项目依赖表';

-- 扫描报告表
DROP TABLE IF EXISTS `scan_report`;
CREATE TABLE `scan_report` (
  `report_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '报告ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `report_content` longtext COMMENT '报告内容(JSON格式)',
  `report_title` varchar(200) COMMENT '报告标题',
  `total_dependencies` bigint(20) DEFAULT 0 COMMENT '总依赖数',
  `cve_count` bigint(20) DEFAULT 0 COMMENT 'CVE漏洞数',
  `high_risk_license_count` bigint(20) DEFAULT 0 COMMENT '高风险许可证数',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`report_id`),
  KEY `idx_project_name` (`project_name`),
  KEY `idx_project_task` (`project_id`, `task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='扫描报告表';

-- 许可证信息表
DROP TABLE IF EXISTS `license_info`;
CREATE TABLE `license_info` (
  `license_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '许可证ID',
  `license_name` varchar(100) NOT NULL COMMENT '许可证名称',
  `license_type` varchar(50) DEFAULT NULL COMMENT '许可证类型',
  `risk_level` varchar(20) DEFAULT 'LOW' COMMENT '风险等级(HIGH高风险 MEDIUM中风险 LOW低风险)',
  `risk_description` text COMMENT '风险描述',
  `is_commercial_use` char(1) DEFAULT '1' COMMENT '是否允许商业使用(0禁止 1允许 2未知)',
  `is_modification` char(1) DEFAULT '1' COMMENT '是否允许修改(0禁止 1允许 2未知)',
  `is_distribution` char(1) DEFAULT '1' COMMENT '是否允许分发(0禁止 1允许 2未知)',
  `license_url` varchar(500) DEFAULT NULL COMMENT '许可证URL',
  `license_text` longtext COMMENT '许可证完整文本',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`license_id`),
  UNIQUE KEY `uk_license_name` (`license_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='许可证信息表';

-- 字典数据配置
-- 许可证风险等级字典
INSERT INTO sys_dict_type VALUES (100, '许可证风险等级', 'license_risk_level', '0', 'admin', sysdate(), '', null, '许可证风险等级分类');
INSERT INTO sys_dict_data VALUES (100, 1, '低风险', '1', 'license_risk_level', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '低风险许可证');
INSERT INTO sys_dict_data VALUES (101, 2, '中风险', '2', 'license_risk_level', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '中风险许可证');
INSERT INTO sys_dict_data VALUES (102, 3, '高风险', '3', 'license_risk_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '高风险许可证(GPL/AGPL等)');

-- CVE严重等级字典
INSERT INTO sys_dict_type VALUES (101, 'CVE严重等级', 'cve_severity_level', '0', 'admin', sysdate(), '', null, 'CVE漏洞严重等级');
INSERT INTO sys_dict_data VALUES (103, 1, '低危', '1', 'cve_severity_level', '', 'info', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分0.1-3.9');
INSERT INTO sys_dict_data VALUES (104, 2, '中危', '2', 'cve_severity_level', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分4.0-6.9');
INSERT INTO sys_dict_data VALUES (105, 3, '高危', '3', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分7.0-8.9');
INSERT INTO sys_dict_data VALUES (106, 4, '严重', '4', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分9.0-10.0');

-- 扫描状态字典
INSERT INTO sys_dict_type VALUES (102, '扫描状态', 'scan_status', '0', 'admin', sysdate(), '', null, '项目扫描状态');
INSERT INTO sys_dict_data VALUES (107, 1, '未扫描', '0', 'scan_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '项目未进行扫描');
INSERT INTO sys_dict_data VALUES (108, 2, '扫描中', '1', 'scan_status', '', 'primary', 'N', '0', 'admin', sysdate(), '', null, '项目正在扫描');
INSERT INTO sys_dict_data VALUES (109, 3, '已完成', '2', 'scan_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '扫描已完成');
INSERT INTO sys_dict_data VALUES (110, 4, '扫描失败', '3', 'scan_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '扫描执行失败');

-- 任务状态字典
INSERT INTO sys_dict_type VALUES (103, '任务状态', 'task_status', '0', 'admin', sysdate(), '', null, '扫描任务状态');
INSERT INTO sys_dict_data VALUES (111, 1, '待执行', '0', 'task_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '任务待执行');
INSERT INTO sys_dict_data VALUES (112, 2, '执行中', '1', 'task_status', '', 'primary', 'N', '0', 'admin', sysdate(), '', null, '任务执行中');
INSERT INTO sys_dict_data VALUES (113, 3, '已完成', '2', 'task_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '任务已完成');
INSERT INTO sys_dict_data VALUES (114, 4, '执行失败', '3', 'task_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '任务执行失败');

-- 许可证类型基础数据(重点关注GPL/AGPL)
INSERT INTO sys_dict_type VALUES (104, '许可证类型', 'license_type', '0', 'admin', sysdate(), '', null, '开源许可证类型');
INSERT INTO sys_dict_data VALUES (115, 1, 'Apache-2.0', 'Apache-2.0', 'license_type', '1', '', 'N', '0', 'admin', sysdate(), '', null, 'Apache许可证2.0版本');
INSERT INTO sys_dict_data VALUES (116, 2, 'MIT', 'MIT', 'license_type', '1', '', 'N', '0', 'admin', sysdate(), '', null, 'MIT许可证');
INSERT INTO sys_dict_data VALUES (117, 3, 'BSD-3-Clause', 'BSD-3-Clause', 'license_type', '1', '', 'N', '0', 'admin', sysdate(), '', null, 'BSD 3条款许可证');
INSERT INTO sys_dict_data VALUES (118, 4, 'GPL-2.0', 'GPL-2.0', 'license_type', '3', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU通用公共许可证v2');
INSERT INTO sys_dict_data VALUES (119, 5, 'GPL-3.0', 'GPL-3.0', 'license_type', '3', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU通用公共许可证v3');
INSERT INTO sys_dict_data VALUES (120, 6, 'AGPL-3.0', 'AGPL-3.0', 'license_type', '3', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU Affero通用公共许可证v3');
INSERT INTO sys_dict_data VALUES (121, 7, 'LGPL-2.1', 'LGPL-2.1', 'license_type', '2', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU宽通用公共许可证v2.1');
INSERT INTO sys_dict_data VALUES (122, 8, 'LGPL-3.0', 'LGPL-3.0', 'license_type', '2', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU宽通用公共许可证v3');

-- 初始化许可证数据
-- GPL系列许可证（高风险）
INSERT INTO license_info (license_name, license_type, risk_level, risk_description, is_commercial_use, is_modification, is_distribution, license_url, status, create_by, create_time) VALUES
('GNU General Public License v2.0', 'GPL-2.0', 'HIGH', 'GPL-2.0是一个强制开源的许可证，要求衍生作品也必须开源。商业使用需要谨慎考虑。', '2', '1', '1', 'https://www.gnu.org/licenses/old-licenses/gpl-2.0.html', '0', 'admin', NOW()),
('GNU General Public License v3.0', 'GPL-3.0', 'HIGH', 'GPL-3.0是GPL-2.0的升级版本，增加了专利保护条款，同样要求衍生作品开源。', '2', '1', '1', 'https://www.gnu.org/licenses/gpl-3.0.html', '0', 'admin', NOW()),
('GNU Affero General Public License v3.0', 'AGPL-3.0', 'HIGH', 'AGPL-3.0是最严格的开源许可证之一，即使是网络服务也要求开源。商业使用风险极高。', '0', '1', '1', 'https://www.gnu.org/licenses/agpl-3.0.html', '0', 'admin', NOW()),
('GNU Lesser General Public License v2.1', 'LGPL-2.1', 'MEDIUM', 'LGPL-2.1允许动态链接到专有软件，但修改LGPL代码本身仍需开源。', '1', '1', '1', 'https://www.gnu.org/licenses/old-licenses/lgpl-2.1.html', '0', 'admin', NOW()),
('GNU Lesser General Public License v3.0', 'LGPL-3.0', 'MEDIUM', 'LGPL-3.0是LGPL-2.1的升级版本，增加了专利保护条款。', '1', '1', '1', 'https://www.gnu.org/licenses/lgpl-3.0.html', '0', 'admin', NOW());

-- 宽松许可证（低风险）
INSERT INTO license_info (license_name, license_type, risk_level, risk_description, is_commercial_use, is_modification, is_distribution, license_url, status, create_by, create_time) VALUES
('Apache License 2.0', 'Apache-2.0', 'LOW', 'Apache-2.0是一个宽松的许可证，允许商业使用、修改和分发，只需保留版权声明。', '1', '1', '1', 'https://www.apache.org/licenses/LICENSE-2.0', '0', 'admin', NOW()),
('MIT License', 'MIT', 'LOW', 'MIT许可证是最宽松的许可证之一，几乎没有限制，只需保留版权声明。', '1', '1', '1', 'https://opensource.org/licenses/MIT', '0', 'admin', NOW()),
('BSD 2-Clause License', 'BSD-2-Clause', 'LOW', 'BSD-2-Clause是一个简单的宽松许可证，允许几乎所有用途。', '1', '1', '1', 'https://opensource.org/licenses/BSD-2-Clause', '0', 'admin', NOW()),
('BSD 3-Clause License', 'BSD-3-Clause', 'LOW', 'BSD-3-Clause在BSD-2-Clause基础上增加了禁止使用项目名称进行推广的条款。', '1', '1', '1', 'https://opensource.org/licenses/BSD-3-Clause', '0', 'admin', NOW()),
('ISC License', 'ISC', 'LOW', 'ISC许可证是一个功能上等同于MIT许可证的宽松许可证。', '1', '1', '1', 'https://opensource.org/licenses/ISC', '0', 'admin', NOW());

-- 其他常见许可证
INSERT INTO license_info (license_name, license_type, risk_level, risk_description, is_commercial_use, is_modification, is_distribution, license_url, status, create_by, create_time) VALUES
('Mozilla Public License 2.0', 'MPL-2.0', 'MEDIUM', 'MPL-2.0是一个弱copyleft许可证，修改的文件需要开源，但可以与专有代码组合。', '1', '1', '1', 'https://www.mozilla.org/en-US/MPL/2.0/', '0', 'admin', NOW()),
('Eclipse Public License 1.0', 'EPL-1.0', 'MEDIUM', 'EPL-1.0是一个弱copyleft许可证，类似于MPL，但有一些不同的条款。', '1', '1', '1', 'https://www.eclipse.org/legal/epl-v10.html', '0', 'admin', NOW()),
('Common Development and Distribution License 1.0', 'CDDL-1.0', 'MEDIUM', 'CDDL-1.0是Sun公司创建的弱copyleft许可证。', '1', '1', '1', 'https://opensource.org/licenses/CDDL-1.0', '0', 'admin', NOW()),
('The Unlicense', 'Unlicense', 'LOW', 'Unlicense将作品放入公共领域，没有任何限制。', '1', '1', '1', 'https://unlicense.org/', '0', 'admin', NOW());

-- 插入测试项目信息数据
INSERT INTO project_info (project_name, project_code, project_path, project_type, project_language, project_description, project_owner, scan_status, last_scan_time, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
('RuoYi管理系统', 'ruoyi-system', '/opt/projects/ruoyi', 'WEB', 'Java', 'RuoYi后台管理系统，基于Spring Boot开发', 'admin', '0', NULL, '0', '0', 'admin', NOW(), 'admin', NOW(), '主要业务系统'),
('电商平台', 'ecommerce-platform', '/opt/projects/ecommerce', 'WEB', 'Java', '电商平台后端服务，包含商品、订单、支付等模块', 'admin', '0', NULL, '0', '0', 'admin', NOW(), 'admin', NOW(), '电商核心系统'),
('移动应用后端', 'mobile-backend', '/opt/projects/mobile-api', 'API', 'Java', '移动应用后端API服务，提供用户、内容等接口', 'admin', '0', NULL, '0', '0', 'admin', NOW(), 'admin', NOW(), '移动端支撑系统');

-- 插入测试扫描任务数据
INSERT INTO scan_task (project_id, task_name, task_status, start_time, end_time, total_dependencies, cve_high_count, cve_medium_count, cve_low_count, license_high_risk_count, license_medium_risk_count, error_msg, create_by, create_time, update_by, update_time, remark) VALUES
(1, '扫描任务-RuoYi管理系统', '2', '2025-01-01 10:00:00', '2025-01-01 10:15:00', 45, 2, 5, 8, 1, 3, NULL, 'admin', NOW(), 'admin', NOW(), '首次扫描完成'),
(2, '扫描任务-电商平台', '0', NULL, NULL, 0, 0, 0, 0, 0, 0, NULL, 'admin', NOW(), 'admin', NOW(), '待执行扫描'),
(3, '扫描任务-移动应用后端', '1', '2025-01-01 14:30:00', NULL, 32, 0, 0, 0, 0, 0, NULL, 'admin', NOW(), 'admin', NOW(), '正在执行扫描'),
(1, '扫描任务-RuoYi管理系统-第二次', '3', '2025-01-01 16:00:00', '2025-01-01 16:05:00', 0, 0, 0, 0, 0, 0, '网络连接超时', 'admin', NOW(), 'admin', NOW(), '扫描失败，需要重试');
