# CVE批量导入工具使用说明

## 概述

本工具提供了多种方式来批量导入历史CVE数据到数据库中，支持Web界面操作、单元测试和命令行工具三种方式。

## 功能特点

- 支持按日期范围导入CVE数据
- 支持按年份导入整年CVE数据
- 支持导入最近N天的CVE数据
- 自动处理API限制和请求延迟
- 支持数据去重和更新
- 详细的日志记录和进度跟踪
- 异步执行，不阻塞主线程

## 使用方式

### 1. Web界面操作（推荐）

1. 启动RuoYi应用
2. 登录系统，进入"系统管理" -> "CVE管理"页面
3. 点击"批量导入"下拉按钮，选择导入方式：
   - **导入最近数据**：可选择最近1-365天的数据
   - **按年份导入**：可选择2020-2025年的数据

4. 选择参数后点击确认，系统将异步执行导入任务
5. 导入过程中可以查看日志了解进度
6. 导入完成后刷新页面查看结果

### 2. 单元测试方式

在`ruoyi-security/src/test/java/com/ruoyi/security/CveBulkImportTest.java`中提供了多个测试方法：

```java
// 导入最近30天数据
@Test
public void importRecentCveData()

// 导入最近一年数据  
@Test
public void importLastYearCveData()

// 导入2024年数据
@Test
public void import2024CveData()

// 导入2023年数据
@Test
public void import2023CveData()
```

使用IDE运行对应的测试方法即可。

### 3. 命令行工具方式

```bash
# 进入项目根目录
cd /path/to/RuoYi

# 导入最近30天数据
mvn spring-boot:run -Dspring-boot.run.main-class=com.ruoyi.security.CveBulkImportRunner -Dspring-boot.run.arguments="--import.recent=30"

# 导入2024年数据
mvn spring-boot:run -Dspring-boot.run.main-class=com.ruoyi.security.CveBulkImportRunner -Dspring-boot.run.arguments="--import.year=2024"

# 导入2023年数据
mvn spring-boot:run -Dspring-boot.run.main-class=com.ruoyi.security.CveBulkImportRunner -Dspring-boot.run.arguments="--import.year=2023"
```

## 导入参数说明

### 按天数导入
- 参数范围：1-365天
- 建议值：不超过90天（数据量考虑）
- 示例：`--import.recent=30`

### 按年份导入
- 参数范围：2020-2025年
- 注意：整年数据量很大，导入时间较长
- 示例：`--import.year=2024`

## 性能优化

1. **请求频率控制**：每次API请求间隔3秒，避免触发API限制
2. **分页处理**：每页50条记录，避免单次请求数据过多
3. **批量处理**：批量保存到数据库，提高效率
4. **错误重试**：API请求失败时自动重试
5. **内存优化**：分页处理避免内存溢出

## 注意事项

1. **网络要求**：需要能够访问NVD API (https://services.nvd.nist.gov)
2. **时间消耗**：大量数据导入需要较长时间，请耐心等待
3. **数据库空间**：确保数据库有足够空间存储CVE数据
4. **API限制**：使用公共API访问，有频率限制
5. **日志监控**：导入过程中注意查看日志了解进度和错误

## 导入数据统计

导入完成后会显示以下统计信息：
- 新增记录数量
- 更新记录数量  
- 错误记录数量
- 总处理记录数量

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 确认能访问NVD API
   - 检查防火墙设置

2. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务正常
   - 检查连接池设置

3. **内存不足**
   - 增加JVM内存设置
   - 减少单次导入的数据量
   - 分批次导入

4. **API请求失败**
   - 检查API地址是否正确
   - 确认API服务可用
   - 检查请求参数格式

### 日志级别

可以通过修改`application.yml`调整日志级别：

```yaml
logging:
  level:
    com.ruoyi.security.util.CveBulkImporter: DEBUG
```

## 技术实现

- **数据源**：NVD API 2.0
- **数据格式**：JSON
- **存储引擎**：MySQL
- **并发处理**：异步执行
- **错误处理**：异常捕获和日志记录

## 更新历史

- v1.0：基础批量导入功能
- v1.1：添加Web界面操作
- v1.2：优化性能和错误处理
- v1.3：添加命令行工具支持
