package com.ruoyi.common.utils;

import com.ruoyi.common.core.domain.entity.SysUser;

/**
 * 安全服务工具类
 * 
 * <AUTHOR>
 */
public class SecurityUtils
{
    /**
     * 获取用户
     **/
    public static SysUser getLoginUser()
    {
        return ShiroUtils.getSysUser();
    }

    /**
     * 获取用户ID
     **/
    public static Long getUserId()
    {
        return ShiroUtils.getUserId();
    }

    /**
     * 获取用户名称
     **/
    public static String getUsername()
    {
        return ShiroUtils.getLoginName();
    }

    /**
     * 获取登录用户名
     **/
    public static String getLoginName()
    {
        return ShiroUtils.getLoginName();
    }

    /**
     * 是否为管理员
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    /**
     * 是否为管理员
     * 
     * @return 结果
     */
    public static boolean isAdmin()
    {
        return isAdmin(getUserId());
    }
}
