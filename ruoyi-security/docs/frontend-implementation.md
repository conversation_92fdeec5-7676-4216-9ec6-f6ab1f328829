# 安全扫描模块前端功能实现说明

## 概述
本文档描述了RuoYi安全扫描模块的前端功能实现情况。所有前端页面都遵循RuoYi框架的设计规范，使用Thymeleaf模板引擎、Bootstrap UI框架和jQuery进行开发。

## 已实现的前端功能

### 1. 项目管理模块 (`/security/project`)

#### 1.1 项目列表页面 (`project.html`)
- **功能**: 项目信息的查询、展示和管理
- **特性**:
  - 支持按项目名称、Git地址、状态等条件搜索
  - 表格展示项目详细信息（项目名称、Git地址、分支、状态、创建时间等）
  - 支持项目的增删改查操作
  - 集成Git连接测试功能
  - 支持数据导出功能
- **权限控制**: `security:project:view`, `security:project:list`, `security:project:add`, `security:project:edit`, `security:project:remove`, `security:project:export`, `security:project:test`

#### 1.2 项目添加页面 (`add.html`)
- **功能**: 新增项目信息
- **表单字段**:
  - 项目名称（必填）
  - Git地址（必填，支持HTTP/HTTPS和SSH协议）
  - 分支名称（默认main）
  - Git用户名和密码/Token（私有仓库需要）
  - 项目描述
  - 状态（启用/禁用）
- **特性**:
  - 实时Git连接测试功能
  - 表单验证
  - 支持私有仓库认证

#### 1.3 项目编辑页面 (`edit.html`)
- **功能**: 修改现有项目信息
- **特性**:
  - 预填充现有项目数据
  - 密码字段留空表示不修改
  - Git连接测试功能
  - 表单验证

### 2. 扫描管理模块 (`/security/scan`)

#### 2.1 扫描管理页面 (`scan.html`)
- **功能**: 扫描任务的管理和监控
- **特性**:
  - 支持按项目名称、扫描状态、时间范围搜索
  - 实时显示扫描进度和状态
  - 扫描状态包括：待扫描、扫描中、已完成、失败
  - 支持开始/停止扫描操作
  - 显示扫描结果统计（依赖总数、CVE数量、高风险许可证数量）
  - 支持查看扫描报告
- **权限控制**: `security:scan:view`, `security:scan:list`, `security:scan:start`, `security:scan:stop`, `security:scan:report`, `security:scan:export`

#### 2.2 项目选择对话框 (`selectProject.html`)
- **功能**: 为新扫描任务选择项目
- **特性**:
  - 项目列表展示和搜索
  - 单选项目
  - 自动创建扫描任务

### 3. 扫描报告模块 (`/security/report`)

#### 3.1 报告列表页面 (`report.html`)
- **功能**: 扫描报告的查询和管理
- **特性**:
  - 支持按项目名称、报告标题、生成时间搜索
  - 显示报告基本信息和风险统计
  - 风险等级可视化显示（高风险、中风险、低风险、安全）
  - 支持查看报告详情和下载报告
- **权限控制**: `security:report:view`, `security:report:list`, `security:report:detail`, `security:report:export`

#### 3.2 报告详情页面 (`detail.html`)
- **功能**: 详细展示扫描报告内容
- **特性**:
  - 报告头部信息展示（项目名称、扫描时间、整体风险等级）
  - 统计概览（依赖总数、CVE漏洞、高风险许可证、漏洞详情）
  - CVE漏洞详情列表（包含严重程度、CVSS评分、影响组件等）
  - 许可证风险详情表格
  - 项目依赖列表
  - 支持下载完整报告

### 4. CVE管理模块 (`/security/cve`)

#### 4.1 CVE管理页面 (`cve.html`)
- **功能**: CVE漏洞数据的查询和管理
- **特性**:
  - 支持按CVE编号、严重程度、影响组件、发布时间搜索
  - CVE编号链接到官方MITRE数据库
  - 严重程度和CVSS评分的可视化显示
  - 支持CVE数据同步功能
  - 支持查看CVE详情
- **权限控制**: `security:cve:view`, `security:cve:list`, `security:cve:sync`, `security:cve:export`

### 5. 许可证管理模块 (`/security/license`)

#### 5.1 许可证列表页面 (`license.html`)
- **功能**: 许可证信息的管理
- **特性**:
  - 支持按许可证名称、风险等级、状态搜索
  - 风险等级可视化显示
  - 显示许可证权限信息（商业使用、修改权限、分发权限）
  - 支持许可证的增删改查操作
- **权限控制**: `security:license:view`, `security:license:list`, `security:license:add`, `security:license:edit`, `security:license:remove`, `security:license:export`

#### 5.2 许可证添加页面 (`add.html`)
- **功能**: 新增许可证信息
- **表单字段**:
  - 许可证名称（必填）
  - 许可证类型（预设常见许可证类型）
  - 风险等级（高风险、中风险、低风险）
  - 风险描述
  - 权限设置（商业使用、修改权限、分发权限）
  - 许可证URL和完整文本
  - 状态和备注
- **特性**:
  - 根据许可证类型自动设置风险等级和描述
  - 智能许可证分析

#### 5.3 许可证编辑页面 (`edit.html`)
- **功能**: 修改现有许可证信息
- **特性**: 预填充现有数据，支持所有字段修改

### 6. 安全仪表板模块 (`/security/dashboard`)

#### 6.1 仪表板页面 (`dashboard.html`)
- **功能**: 安全扫描数据的可视化展示
- **特性**:
  - 统计卡片显示（项目总数、扫描总数、发现漏洞、风险许可证）
  - 风险分布饼图
  - 扫描状态统计图
  - CVE发现趋势图（最近30天）
  - 最近扫描记录列表
  - 高风险项目列表
  - 自动数据刷新（每30秒）
- **权限控制**: `security:dashboard:view`, `security:dashboard:list`

## 菜单配置

### 菜单SQL脚本 (`security_menu.sql`)
已创建完整的菜单配置SQL脚本，包括：

1. **一级菜单**: 安全扫描（图标：fa-shield）
2. **二级菜单**:
   - 项目管理（图标：fa-folder-open）
   - 扫描管理（图标：fa-search）
   - 扫描报告（图标：fa-file-text-o）
   - CVE管理（图标：fa-bug）
   - 许可证管理（图标：fa-legal）
   - 安全仪表板（图标：fa-dashboard）

3. **按钮权限**: 为每个模块配置了详细的按钮权限（查询、新增、修改、删除、导出等）

4. **角色权限**: 自动为管理员角色分配所有安全扫描模块权限

## 技术特性

### 1. 响应式设计
- 所有页面都采用Bootstrap响应式布局
- 支持不同屏幕尺寸的设备访问

### 2. 权限集成
- 完整集成RuoYi的Shiro权限系统
- 按钮级别的权限控制
- 菜单权限控制

### 3. 数据可视化
- 使用Chart.js进行数据图表展示
- 支持饼图、折线图、环形图等多种图表类型
- 实时数据更新

### 4. 用户体验
- AJAX异步操作，无页面刷新
- 加载状态提示
- 操作确认对话框
- 表单验证和错误提示
- 搜索和分页功能

### 5. 国际化支持
- 所有文本都使用中文
- 遵循RuoYi的国际化规范

## 部署说明

1. **菜单配置**: 需要执行 `ruoyi-security/sql/security_menu.sql` 脚本来添加菜单
2. **权限配置**: 菜单SQL脚本会自动为管理员角色分配权限
3. **静态资源**: 所有前端页面都放在 `ruoyi-admin/src/main/resources/templates/security/` 目录下
4. **依赖**: 仪表板页面使用了Chart.js，通过CDN引入

## 后续工作

1. **后端集成**: 需要确保后端Controller提供相应的接口
2. **测试**: 需要进行完整的功能测试和权限测试
3. **优化**: 根据实际使用情况进行性能优化和用户体验改进

## 总结

安全扫描模块的前端功能已经完整实现，包括6个主要功能模块的所有页面。所有页面都遵循RuoYi框架的设计规范，具有良好的用户体验和完整的权限控制。用户可以通过这些页面完成项目管理、扫描任务管理、报告查看、CVE管理、许可证管理和安全监控等所有功能。
