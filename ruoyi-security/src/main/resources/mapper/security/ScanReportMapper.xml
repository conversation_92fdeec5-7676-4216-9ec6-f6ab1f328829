<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.security.mapper.ScanReportMapper">
    
    <resultMap type="ScanReport" id="ScanReportResult">
        <result property="reportId"                 column="report_id"                 />
        <result property="projectId"               column="project_id"               />
        <result property="taskId"                  column="task_id"                  />
        <result property="projectName"             column="project_name"             />
        <result property="reportContent"           column="report_content"           />
        <result property="reportTitle"             column="report_title"             />
        <result property="totalDependencies"       column="total_dependencies"       />
        <result property="cveCount"                column="cve_count"                />
        <result property="highRiskLicenseCount"    column="high_risk_license_count"    />
        <result property="createBy"                column="create_by"                />
        <result property="createTime"              column="create_time"              />
    </resultMap>

    <sql id="selectScanReportVo">
        select report_id, project_id, task_id, project_name, report_content, 
               report_title, total_dependencies, cve_count, high_risk_license_count,
               create_by, create_time 
        from scan_report
    </sql>

    <select id="selectScanReportList" parameterType="ScanReport" resultMap="ScanReportResult">
        <include refid="selectScanReportVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="reportTitle != null  and reportTitle != ''"> and report_title like concat('%', #{reportTitle}, '%')</if>
            <if test="totalDependencies != null "> and total_dependencies = #{totalDependencies}</if>
            <if test="cveCount != null "> and cve_count = #{cveCount}</if>
            <if test="highRiskLicenseCount != null "> and high_risk_license_count = #{highRiskLicenseCount}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectScanReportByReportId" parameterType="Long" resultMap="ScanReportResult">
        <include refid="selectScanReportVo"/>
        where report_id = #{reportId}
    </select>

    <select id="selectScanReportByProjectName" parameterType="String" resultMap="ScanReportResult">
        <include refid="selectScanReportVo"/>
        where project_name like concat('%', #{projectName}, '%')
        order by create_time desc
    </select>
        
    <insert id="insertScanReport" parameterType="ScanReport" useGeneratedKeys="true" keyProperty="reportId">
        insert into scan_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="reportContent != null">report_content,</if>
            <if test="reportTitle != null">report_title,</if>
            <if test="totalDependencies != null">total_dependencies,</if>
            <if test="cveCount != null">cve_count,</if>
            <if test="highRiskLicenseCount != null">high_risk_license_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="reportContent != null">#{reportContent},</if>
            <if test="reportTitle != null">#{reportTitle},</if>
            <if test="totalDependencies != null">#{totalDependencies},</if>
            <if test="cveCount != null">#{cveCount},</if>
            <if test="highRiskLicenseCount != null">#{highRiskLicenseCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateScanReport" parameterType="ScanReport">
        update scan_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="reportContent != null">report_content = #{reportContent},</if>
            <if test="reportTitle != null">report_title = #{reportTitle},</if>
            <if test="totalDependencies != null">total_dependencies = #{totalDependencies},</if>
            <if test="cveCount != null">cve_count = #{cveCount},</if>
            <if test="highRiskLicenseCount != null">high_risk_license_count = #{highRiskLicenseCount},</if>
        </trim>
        where report_id = #{reportId}
    </update>

    <delete id="deleteScanReportByReportId" parameterType="Long">
        delete from scan_report where report_id = #{reportId}
    </delete>

    <delete id="deleteScanReportByReportIds" parameterType="String">
        delete from scan_report where report_id in 
        <foreach item="reportId" collection="array" open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </delete>

    <!-- 根据项目ID和任务ID查询报告 -->
    <select id="selectScanReportByProjectAndTask" resultMap="ScanReportResult">
        <include refid="selectScanReportVo"/>
        where project_id = #{projectId} and task_id = #{taskId}
    </select>

    <!-- 统计报告数量 -->
    <select id="countScanReportByProject" parameterType="Long" resultType="int">
        select count(*) from scan_report where project_id = #{projectId}
    </select>

    <!-- 获取最新的扫描报告 -->
    <select id="selectLatestScanReportByProject" parameterType="Long" resultMap="ScanReportResult">
        <include refid="selectScanReportVo"/>
        where project_id = #{projectId}
        order by create_time desc
        limit 1
    </select>

    <!-- 批量删除项目相关报告 -->
    <delete id="deleteScanReportByProjectId" parameterType="Long">
        delete from scan_report where project_id = #{projectId}
    </delete>

    <!-- 统计各项目的报告数量 -->
    <select id="countReportsByProject" resultType="java.util.Map">
        select project_name, count(*) as reportCount
        from scan_report
        group by project_name
        order by reportCount desc
    </select>

    <!-- 获取报告统计信息 -->
    <select id="getReportStatistics" resultType="java.util.Map">
        select 
            count(*) as totalReports,
            sum(total_dependencies) as totalDependencies,
            sum(cve_count) as totalCveCount,
            sum(high_risk_license_count) as totalHighRiskLicenses,
            avg(total_dependencies) as avgDependencies,
            avg(cve_count) as avgCveCount
        from scan_report
    </select>

    <!-- 根据时间范围统计报告 -->
    <select id="countReportsByDateRange" resultType="java.util.Map">
        select 
            date_format(create_time, '%Y-%m-%d') as reportDate,
            count(*) as reportCount
        from scan_report
        where create_time between #{startDate} and #{endDate}
        group by date_format(create_time, '%Y-%m-%d')
        order by reportDate desc
    </select>
</mapper>
