<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.security.mapper.ProjectDependencyMapper">
    
    <resultMap type="ProjectDependency" id="ProjectDependencyResult">
        <result property="dependencyId"         column="dependency_id"         />
        <result property="projectId"            column="project_id"            />
        <result property="taskId"               column="task_id"               />
        <result property="groupId"              column="group_id"              />
        <result property="artifactId"           column="artifact_id"           />
        <result property="version"              column="version"               />
        <result property="licenseType"          column="license_type"          />
        <result property="licenseRiskLevel"     column="license_risk_level"     />
        <result property="hasCve"               column="has_cve"               />
        <result property="cveCount"             column="cve_count"             />
        <result property="maxCvssScore"         column="max_cvss_score"         />
        <result property="cveDetails"           column="cve_details"           />
        <result property="createTime"           column="create_time"           />
    </resultMap>

    <sql id="selectProjectDependencyVo">
        select dependency_id, project_id, task_id, group_id, artifact_id, version, 
               license_type, license_risk_level, has_cve, cve_count, max_cvss_score, 
               cve_details, create_time 
        from project_dependency
    </sql>

    <select id="selectProjectDependencyList" parameterType="ProjectDependency" resultMap="ProjectDependencyResult">
        <include refid="selectProjectDependencyVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="groupId != null  and groupId != ''"> and group_id like concat('%', #{groupId}, '%')</if>
            <if test="artifactId != null  and artifactId != ''"> and artifact_id like concat('%', #{artifactId}, '%')</if>
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="licenseType != null  and licenseType != ''"> and license_type like concat('%', #{licenseType}, '%')</if>
            <if test="licenseRiskLevel != null  and licenseRiskLevel != ''"> and license_risk_level = #{licenseRiskLevel}</if>
            <if test="hasCve != null  and hasCve != ''"> and has_cve = #{hasCve}</if>
            <if test="cveCount != null "> and cve_count = #{cveCount}</if>
            <if test="maxCvssScore != null "> and max_cvss_score = #{maxCvssScore}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectProjectDependencyByDependencyId" parameterType="Long" resultMap="ProjectDependencyResult">
        <include refid="selectProjectDependencyVo"/>
        where dependency_id = #{dependencyId}
    </select>

    <select id="selectProjectDependencyByTaskId" parameterType="Long" resultMap="ProjectDependencyResult">
        <include refid="selectProjectDependencyVo"/>
        where task_id = #{taskId}
        order by group_id, artifact_id
    </select>
        
    <insert id="insertProjectDependency" parameterType="ProjectDependency" useGeneratedKeys="true" keyProperty="dependencyId">
        insert into project_dependency
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="groupId != null and groupId != ''">group_id,</if>
            <if test="artifactId != null and artifactId != ''">artifact_id,</if>
            <if test="version != null and version != ''">version,</if>
            <if test="licenseType != null">license_type,</if>
            <if test="licenseRiskLevel != null">license_risk_level,</if>
            <if test="hasCve != null">has_cve,</if>
            <if test="cveCount != null">cve_count,</if>
            <if test="maxCvssScore != null">max_cvss_score,</if>
            <if test="cveDetails != null">cve_details,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="groupId != null and groupId != ''">#{groupId},</if>
            <if test="artifactId != null and artifactId != ''">#{artifactId},</if>
            <if test="version != null and version != ''">#{version},</if>
            <if test="licenseType != null">#{licenseType},</if>
            <if test="licenseRiskLevel != null">#{licenseRiskLevel},</if>
            <if test="hasCve != null">#{hasCve},</if>
            <if test="cveCount != null">#{cveCount},</if>
            <if test="maxCvssScore != null">#{maxCvssScore},</if>
            <if test="cveDetails != null">#{cveDetails},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateProjectDependency" parameterType="ProjectDependency">
        update project_dependency
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="groupId != null and groupId != ''">group_id = #{groupId},</if>
            <if test="artifactId != null and artifactId != ''">artifact_id = #{artifactId},</if>
            <if test="version != null and version != ''">version = #{version},</if>
            <if test="licenseType != null">license_type = #{licenseType},</if>
            <if test="licenseRiskLevel != null">license_risk_level = #{licenseRiskLevel},</if>
            <if test="hasCve != null">has_cve = #{hasCve},</if>
            <if test="cveCount != null">cve_count = #{cveCount},</if>
            <if test="maxCvssScore != null">max_cvss_score = #{maxCvssScore},</if>
            <if test="cveDetails != null">cve_details = #{cveDetails},</if>
        </trim>
        where dependency_id = #{dependencyId}
    </update>

    <delete id="deleteProjectDependencyByDependencyId" parameterType="Long">
        delete from project_dependency where dependency_id = #{dependencyId}
    </delete>

    <delete id="deleteProjectDependencyByDependencyIds" parameterType="String">
        delete from project_dependency where dependency_id in 
        <foreach item="dependencyId" collection="array" open="(" separator="," close=")">
            #{dependencyId}
        </foreach>
    </delete>

    <!-- 批量插入项目依赖 -->
    <insert id="batchInsertProjectDependency" parameterType="java.util.List">
        insert into project_dependency (project_id, task_id, group_id, artifact_id, version, 
                                       license_type, license_risk_level, has_cve, cve_count, 
                                       max_cvss_score, cve_details, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId}, #{item.taskId}, #{item.groupId}, #{item.artifactId}, #{item.version}, 
             #{item.licenseType}, #{item.licenseRiskLevel}, #{item.hasCve}, #{item.cveCount}, 
             #{item.maxCvssScore}, #{item.cveDetails}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 根据项目ID和任务ID删除依赖 -->
    <delete id="deleteProjectDependencyByProjectAndTask">
        delete from project_dependency 
        where project_id = #{projectId} and task_id = #{taskId}
    </delete>

    <!-- 统计项目依赖数量 -->
    <select id="countProjectDependencyByTaskId" parameterType="Long" resultType="int">
        select count(*) from project_dependency where task_id = #{taskId}
    </select>

    <!-- 统计CVE数量 -->
    <select id="countCveByTaskId" parameterType="Long" resultType="java.util.Map">
        select 
            sum(case when has_cve = '1' then 1 else 0 end) as hasCveCount,
            sum(cve_count) as totalCveCount,
            max(max_cvss_score) as maxCvssScore
        from project_dependency 
        where task_id = #{taskId}
    </select>

    <!-- 统计许可证风险 -->
    <select id="countLicenseRiskByTaskId" parameterType="Long" resultType="java.util.Map">
        select 
            sum(case when license_risk_level = '3' then 1 else 0 end) as highRiskCount,
            sum(case when license_risk_level = '2' then 1 else 0 end) as mediumRiskCount,
            sum(case when license_risk_level = '1' then 1 else 0 end) as lowRiskCount
        from project_dependency 
        where task_id = #{taskId}
    </select>
</mapper>
