<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.security.mapper.CveInfoMapper">
    
    <resultMap type="CveInfo" id="CveInfoResult">
        <result property="cveId"                column="cve_id"                />
        <result property="cveDesc"              column="cve_desc"              />
        <result property="cvssScore"            column="cvss_score"            />
        <result property="severityLevel"        column="severity_level"        />
        <result property="publishDate"          column="publish_date"          />
        <result property="updateDate"           column="update_date"           />
        <result property="affectedComponents"   column="affected_components"   />
        <result property="references"           column="references"            />
        <result property="status"               column="status"                />
        <result property="createTime"           column="create_time"           />
        <result property="updateTime"           column="update_time"           />
    </resultMap>

    <sql id="selectCveInfoVo">
        select cve_id, cve_desc, cvss_score, severity_level, publish_date, update_date, 
               affected_components, references, status, create_time, update_time 
        from cve_info
    </sql>

    <select id="selectCveInfoList" parameterType="CveInfo" resultMap="CveInfoResult">
        <include refid="selectCveInfoVo"/>
        <where>  
            <if test="cveId != null  and cveId != ''"> and cve_id like concat('%', #{cveId}, '%')</if>
            <if test="cveDesc != null  and cveDesc != ''"> and cve_desc like concat('%', #{cveDesc}, '%')</if>
            <if test="cvssScore != null "> and cvss_score = #{cvssScore}</if>
            <if test="severityLevel != null  and severityLevel != ''"> and severity_level = #{severityLevel}</if>
            <if test="publishDate != null "> and publish_date = #{publishDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(publish_date,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(publish_date,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
            </if>
        </where>
        order by publish_date desc, cvss_score desc
    </select>
    
    <select id="selectCveInfoByCveId" parameterType="String" resultMap="CveInfoResult">
        <include refid="selectCveInfoVo"/>
        where cve_id = #{cveId}
    </select>
        
    <insert id="insertCveInfo" parameterType="CveInfo">
        insert into cve_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cveId != null and cveId != ''">cve_id,</if>
            <if test="cveDesc != null">cve_desc,</if>
            <if test="cvssScore != null">cvss_score,</if>
            <if test="severityLevel != null">severity_level,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="affectedComponents != null">affected_components,</if>
            <if test="references != null">references,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cveId != null and cveId != ''">#{cveId},</if>
            <if test="cveDesc != null">#{cveDesc},</if>
            <if test="cvssScore != null">#{cvssScore},</if>
            <if test="severityLevel != null">#{severityLevel},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="affectedComponents != null">#{affectedComponents},</if>
            <if test="references != null">#{references},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCveInfo" parameterType="CveInfo">
        update cve_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="cveDesc != null">cve_desc = #{cveDesc},</if>
            <if test="cvssScore != null">cvss_score = #{cvssScore},</if>
            <if test="severityLevel != null">severity_level = #{severityLevel},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="affectedComponents != null">affected_components = #{affectedComponents},</if>
            <if test="references != null">references = #{references},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where cve_id = #{cveId}
    </update>

    <delete id="deleteCveInfoByCveId" parameterType="String">
        delete from cve_info where cve_id = #{cveId}
    </delete>

    <delete id="deleteCveInfoByCveIds" parameterType="String">
        delete from cve_info where cve_id in 
        <foreach item="cveId" collection="array" open="(" separator="," close=")">
            #{cveId}
        </foreach>
    </delete>

    <!-- 根据CVSS评分范围查询CVE -->
    <select id="selectCveInfoByCvssRange" resultMap="CveInfoResult">
        <include refid="selectCveInfoVo"/>
        where cvss_score between #{minScore} and #{maxScore}
        and status = '0'
        order by cvss_score desc
    </select>

    <!-- 根据严重等级查询CVE -->
    <select id="selectCveInfoBySeverityLevel" parameterType="String" resultMap="CveInfoResult">
        <include refid="selectCveInfoVo"/>
        where severity_level = #{severityLevel}
        and status = '0'
        order by publish_date desc
    </select>

    <!-- 批量插入CVE信息 -->
    <insert id="batchInsertCveInfo" parameterType="java.util.List">
        insert into cve_info (cve_id, cve_desc, cvss_score, severity_level, publish_date, 
                              update_date, affected_components, references, status, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.cveId}, #{item.cveDesc}, #{item.cvssScore}, #{item.severityLevel}, 
             #{item.publishDate}, #{item.updateDate}, #{item.affectedComponents}, 
             #{item.references}, #{item.status}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>
