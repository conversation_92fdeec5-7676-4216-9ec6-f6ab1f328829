<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.security.mapper.ScanTaskMapper">
    
    <resultMap type="ScanTask" id="ScanTaskResult">
        <result property="taskId"                   column="task_id"                   />
        <result property="projectId"               column="project_id"               />
        <result property="taskName"                column="task_name"                />
        <result property="taskStatus"              column="task_status"              />
        <result property="startTime"               column="start_time"               />
        <result property="endTime"                 column="end_time"                 />
        <result property="totalDependencies"       column="total_dependencies"       />
        <result property="cveHighCount"            column="cve_high_count"            />
        <result property="cveMediumCount"          column="cve_medium_count"          />
        <result property="cveLowCount"             column="cve_low_count"             />
        <result property="licenseHighRiskCount"    column="license_high_risk_count"    />
        <result property="licenseMediumRiskCount"  column="license_medium_risk_count"  />
        <result property="errorMsg"                column="error_msg"                />
        <result property="projectName"             column="project_name"             />
        <result property="createBy"                column="create_by"                />
        <result property="createTime"              column="create_time"              />
        <result property="updateBy"                column="update_by"                />
        <result property="updateTime"              column="update_time"              />
        <result property="remark"                  column="remark"                   />
    </resultMap>

    <sql id="selectScanTaskVo">
        select st.task_id, st.project_id, st.task_name, st.task_status, st.start_time, st.end_time, 
               st.total_dependencies, st.cve_high_count, st.cve_medium_count, st.cve_low_count, 
               st.license_high_risk_count, st.license_medium_risk_count, st.error_msg, 
               st.create_by, st.create_time, st.update_by, st.update_time, st.remark,
               pi.project_name
        from scan_task st
        left join project_info pi on st.project_id = pi.project_id
    </sql>

    <!-- 查询项目扫描任务列表，包含未扫描的项目 -->
    <select id="selectScanTaskList" parameterType="ScanTask" resultMap="ScanTaskResult">
        select pi.project_id, pi.project_name,
               COALESCE(st.task_id, 0) as task_id,
               COALESCE(st.task_name, CONCAT('扫描任务-', pi.project_name)) as task_name,
               COALESCE(st.task_status, '0') as task_status,
               st.start_time, st.end_time,
               COALESCE(st.total_dependencies, 0) as total_dependencies,
               COALESCE(st.cve_high_count, 0) as cve_high_count,
               COALESCE(st.cve_medium_count, 0) as cve_medium_count,
               COALESCE(st.cve_low_count, 0) as cve_low_count,
               COALESCE(st.license_high_risk_count, 0) as license_high_risk_count,
               COALESCE(st.license_medium_risk_count, 0) as license_medium_risk_count,
               st.error_msg, st.create_by, st.create_time, st.update_by, st.update_time, st.remark
        from project_info pi
        left join scan_task st on pi.project_id = st.project_id
        <where>
            <!-- 只显示状态正常的项目 -->
            and pi.status = '0' and pi.del_flag = '0'
            <if test="projectId != null "> and pi.project_id = #{projectId}</if>
            <if test="taskName != null  and taskName != ''"> and (st.task_name like concat('%', #{taskName}, '%') or pi.project_name like concat('%', #{taskName}, '%'))</if>
            <if test="taskStatus != null  and taskStatus != ''"> and COALESCE(st.task_status, '0') = #{taskStatus}</if>
            <if test="startTime != null "> and st.start_time = #{startTime}</if>
            <if test="endTime != null "> and st.end_time = #{endTime}</if>
            <if test="projectName != null and projectName != ''"> and pi.project_name like concat('%', #{projectName}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(st.start_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(st.start_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
            </if>
        </where>
        order by pi.project_id asc
    </select>
    
    <select id="selectScanTaskByTaskId" parameterType="Long" resultMap="ScanTaskResult">
        <include refid="selectScanTaskVo"/>
        where st.task_id = #{taskId}
    </select>

    <!-- 根据项目ID查询扫描任务 -->
    <select id="selectScanTaskByProjectId" parameterType="Long" resultMap="ScanTaskResult">
        <include refid="selectScanTaskVo"/>
        where st.project_id = #{projectId}
    </select>
        
    <insert id="insertScanTask" parameterType="ScanTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into scan_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="totalDependencies != null">total_dependencies,</if>
            <if test="cveHighCount != null">cve_high_count,</if>
            <if test="cveMediumCount != null">cve_medium_count,</if>
            <if test="cveLowCount != null">cve_low_count,</if>
            <if test="licenseHighRiskCount != null">license_high_risk_count,</if>
            <if test="licenseMediumRiskCount != null">license_medium_risk_count,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="totalDependencies != null">#{totalDependencies},</if>
            <if test="cveHighCount != null">#{cveHighCount},</if>
            <if test="cveMediumCount != null">#{cveMediumCount},</if>
            <if test="cveLowCount != null">#{cveLowCount},</if>
            <if test="licenseHighRiskCount != null">#{licenseHighRiskCount},</if>
            <if test="licenseMediumRiskCount != null">#{licenseMediumRiskCount},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateScanTask" parameterType="ScanTask">
        update scan_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="totalDependencies != null">total_dependencies = #{totalDependencies},</if>
            <if test="cveHighCount != null">cve_high_count = #{cveHighCount},</if>
            <if test="cveMediumCount != null">cve_medium_count = #{cveMediumCount},</if>
            <if test="cveLowCount != null">cve_low_count = #{cveLowCount},</if>
            <if test="licenseHighRiskCount != null">license_high_risk_count = #{licenseHighRiskCount},</if>
            <if test="licenseMediumRiskCount != null">license_medium_risk_count = #{licenseMediumRiskCount},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteScanTaskByTaskId" parameterType="Long">
        delete from scan_task where task_id = #{taskId}
    </delete>

    <delete id="deleteScanTaskByTaskIds" parameterType="String">
        delete from scan_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>
