package com.ruoyi.security.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目信息对象 project_info
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ProjectInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 项目ID */
    private Long projectId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 项目编码 */
    @Excel(name = "项目编码")
    private String projectCode;

    /** GitLab地址 */
    @Excel(name = "GitLab地址")
    private String gitUrl;

    /** Git分支 */
    @Excel(name = "Git分支")
    private String gitBranch;

    /** GitLab用户名 */
    @Excel(name = "GitLab用户名")
    private String gitUsername;

    /** GitLab密码或Token(加密) */
    private String gitPassword;

    /** 项目描述 */
    @Excel(name = "项目描述")
    private String projectDesc;

    /** 项目负责人 */
    @Excel(name = "项目负责人")
    private String projectOwner;

    /** 扫描状态(0未扫描 1扫描中 2已完成 3扫描失败) */
    @Excel(name = "扫描状态", readConverterExp = "0=未扫描,1=扫描中,2=已完成,3=扫描失败")
    private String scanStatus;

    /** 最后扫描时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后扫描时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastScanTime;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }
    public void setProjectCode(String projectCode) 
    {
        this.projectCode = projectCode;
    }

    public String getProjectCode() 
    {
        return projectCode;
    }
    public void setGitUrl(String gitUrl) 
    {
        this.gitUrl = gitUrl;
    }

    public String getGitUrl() 
    {
        return gitUrl;
    }
    public void setGitBranch(String gitBranch) 
    {
        this.gitBranch = gitBranch;
    }

    public String getGitBranch() 
    {
        return gitBranch;
    }
    public void setGitUsername(String gitUsername) 
    {
        this.gitUsername = gitUsername;
    }

    public String getGitUsername() 
    {
        return gitUsername;
    }
    public void setGitPassword(String gitPassword) 
    {
        this.gitPassword = gitPassword;
    }

    public String getGitPassword() 
    {
        return gitPassword;
    }
    public void setProjectDesc(String projectDesc) 
    {
        this.projectDesc = projectDesc;
    }

    public String getProjectDesc() 
    {
        return projectDesc;
    }
    public void setProjectOwner(String projectOwner) 
    {
        this.projectOwner = projectOwner;
    }

    public String getProjectOwner() 
    {
        return projectOwner;
    }
    public void setScanStatus(String scanStatus) 
    {
        this.scanStatus = scanStatus;
    }

    public String getScanStatus() 
    {
        return scanStatus;
    }
    public void setLastScanTime(Date lastScanTime) 
    {
        this.lastScanTime = lastScanTime;
    }

    public Date getLastScanTime() 
    {
        return lastScanTime;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("projectId", getProjectId())
            .append("projectName", getProjectName())
            .append("projectCode", getProjectCode())
            .append("gitUrl", getGitUrl())
            .append("gitBranch", getGitBranch())
            .append("gitUsername", getGitUsername())
            .append("gitPassword", getGitPassword())
            .append("projectDesc", getProjectDesc())
            .append("projectOwner", getProjectOwner())
            .append("scanStatus", getScanStatus())
            .append("lastScanTime", getLastScanTime())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
