package com.ruoyi.security.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.domain.ScanTask;
import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.security.service.IScanService;
import com.ruoyi.security.service.IProjectInfoService;
import com.ruoyi.security.service.IScanTaskService;
import com.ruoyi.security.service.IProjectDependencyService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 扫描管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/security/scan")
public class ScanController extends BaseController
{
    @Autowired
    private IScanService scanService;

    @Autowired
    private IProjectInfoService projectInfoService;

    @Autowired
    private IScanTaskService scanTaskService;

    @Autowired
    private IProjectDependencyService projectDependencyService;

    /**
     * 执行项目扫描
     */
    @PreAuthorize("@ss.hasPermi('security:scan:execute')")
    @Log(title = "项目扫描", businessType = BusinessType.OTHER)
    @PostMapping("/execute/{projectId}")
    public AjaxResult executeScan(@PathVariable("projectId") Long projectId)
    {
        ProjectInfo projectInfo = projectInfoService.selectProjectInfoByProjectId(projectId);
        if (projectInfo == null)
        {
            return error("项目不存在");
        }

        // 检查项目是否正在扫描
        if ("1".equals(projectInfo.getScanStatus()))
        {
            return error("项目正在扫描中，请稍后再试");
        }

        try
        {
            ScanTask scanTask = scanService.executeScan(projectInfo);
            return success("扫描任务已启动", scanTask);
        }
        catch (Exception e)
        {
            logger.error("启动扫描任务失败", e);
            return error("启动扫描任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询扫描任务列表
     */
    @PreAuthorize("@ss.hasPermi('security:scan:list')")
    @PostMapping("/task/list")
    public TableDataInfo taskList(ScanTask scanTask)
    {
        startPage();
        List<ScanTask> list = scanTaskService.selectScanTaskList(scanTask);
        return getDataTable(list);
    }

    /**
     * 查询扫描任务列表 (前端页面使用)
     */
    @PreAuthorize("@ss.hasPermi('security:scan:list')")
    @PostMapping("/list")
    public TableDataInfo list(ScanTask scanTask)
    {
        startPage();
        List<ScanTask> list = scanTaskService.selectScanTaskList(scanTask);
        return getDataTable(list);
    }

    /**
     * 获取扫描任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('security:scan:query')")
    @GetMapping("/task/{taskId}")
    public AjaxResult getTaskInfo(@PathVariable("taskId") Long taskId)
    {
        ScanTask scanTask = scanTaskService.selectScanTaskByTaskId(taskId);
        if (scanTask != null)
        {
            return success(scanTask);
        }
        return error("扫描任务不存在");
    }

    /**
     * 获取扫描状态
     */
    @PreAuthorize("@ss.hasPermi('security:scan:query')")
    @GetMapping("/status/{taskId}")
    public AjaxResult getScanStatus(@PathVariable("taskId") Long taskId)
    {
        ScanTask scanTask = scanService.getScanStatus(taskId);
        if (scanTask != null)
        {
            return success(scanTask);
        }
        return error("扫描任务不存在");
    }

    /**
     * 停止扫描任务
     */
    @PreAuthorize("@ss.hasPermi('security:scan:stop')")
    @Log(title = "停止扫描", businessType = BusinessType.OTHER)
    @PostMapping("/stop/{taskId}")
    public AjaxResult stopScan(@PathVariable("taskId") Long taskId)
    {
        boolean result = scanService.stopScan(taskId);
        if (result)
        {
            return success("扫描任务已停止");
        }
        return error("停止扫描任务失败");
    }

    /**
     * 停止扫描任务 (前端页面使用)
     */
    @PreAuthorize("@ss.hasPermi('security:scan:stop')")
    @Log(title = "停止扫描", businessType = BusinessType.OTHER)
    @PostMapping("/stop")
    public AjaxResult stopScanByForm(Long scanId)
    {
        if (scanId == null)
        {
            return error("扫描任务ID不能为空");
        }
        boolean result = scanService.stopScan(scanId);
        if (result)
        {
            return success("扫描任务已停止");
        }
        return error("停止扫描任务失败");
    }

    /**
     * 创建扫描任务 (前端页面使用)
     */
    @PreAuthorize("@ss.hasPermi('security:scan:add')")
    @Log(title = "创建扫描任务", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createScanTask(Long projectId, String projectName)
    {
        if (projectId == null)
        {
            return error("项目ID不能为空");
        }

        ProjectInfo projectInfo = projectInfoService.selectProjectInfoByProjectId(projectId);
        if (projectInfo == null)
        {
            return error("项目不存在");
        }

        try
        {
            // 创建扫描任务
            ScanTask scanTask = new ScanTask();
            scanTask.setProjectId(projectId);
            scanTask.setTaskName("扫描任务-" + projectInfo.getProjectName());
            scanTask.setTaskStatus("0"); // 待执行
            scanTask.setCreateBy(getUsername());
            scanTask.setCreateTime(new java.util.Date());

            int result = scanTaskService.insertScanTask(scanTask);
            if (result > 0)
            {
                return success("扫描任务创建成功", scanTask);
            }
            else
            {
                return error("创建扫描任务失败");
            }
        }
        catch (Exception e)
        {
            logger.error("创建扫描任务失败", e);
            return error("创建扫描任务失败: " + e.getMessage());
        }
    }

    /**
     * 启动扫描任务 (前端页面使用)
     */
    @PreAuthorize("@ss.hasPermi('security:scan:execute')")
    @Log(title = "启动扫描", businessType = BusinessType.OTHER)
    @PostMapping("/start")
    public AjaxResult startScanByForm(Long scanId)
    {
        if (scanId == null)
        {
            return error("扫描任务ID不能为空");
        }

        // 根据scanId获取对应的项目信息
        ScanTask scanTask = scanTaskService.selectScanTaskByTaskId(scanId);
        if (scanTask == null)
        {
            return error("扫描任务不存在");
        }

        ProjectInfo projectInfo = projectInfoService.selectProjectInfoByProjectId(scanTask.getProjectId());
        if (projectInfo == null)
        {
            return error("项目不存在");
        }

        // 检查项目是否正在扫描
        if ("1".equals(projectInfo.getScanStatus()))
        {
            return error("项目正在扫描中，请稍后再试");
        }

        try
        {
            ScanTask newScanTask = scanService.executeScan(projectInfo);
            return success("扫描任务已启动", newScanTask);
        }
        catch (Exception e)
        {
            logger.error("启动扫描任务失败", e);
            return error("启动扫描任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询项目依赖列表
     */
    @PreAuthorize("@ss.hasPermi('security:scan:dependency')")
    @PostMapping("/dependency/list")
    public TableDataInfo dependencyList(ProjectDependency projectDependency)
    {
        startPage();
        List<ProjectDependency> list = projectDependencyService.selectProjectDependencyList(projectDependency);
        return getDataTable(list);
    }

    /**
     * 根据任务ID查询依赖列表
     */
    @PreAuthorize("@ss.hasPermi('security:scan:dependency')")
    @GetMapping("/dependency/task/{taskId}")
    public TableDataInfo getDependencyByTaskId(@PathVariable("taskId") Long taskId)
    {
        startPage();
        List<ProjectDependency> list = projectDependencyService.selectProjectDependencyByTaskId(taskId);
        return getDataTable(list);
    }

    /**
     * 获取依赖详细信息
     */
    @PreAuthorize("@ss.hasPermi('security:scan:dependency')")
    @GetMapping("/dependency/{dependencyId}")
    public AjaxResult getDependencyInfo(@PathVariable("dependencyId") Long dependencyId)
    {
        ProjectDependency dependency = projectDependencyService.selectProjectDependencyByDependencyId(dependencyId);
        if (dependency != null)
        {
            return success(dependency);
        }
        return error("依赖信息不存在");
    }
}
