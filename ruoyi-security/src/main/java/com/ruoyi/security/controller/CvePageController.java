package com.ruoyi.security.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import com.ruoyi.security.domain.CveInfo;
import com.ruoyi.security.service.ICveInfoService;

/**
 * CVE页面跳转控制器
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Controller
@RequestMapping("/security/cve")
public class CvePageController
{
    @Autowired
    private ICveInfoService cveInfoService;

    /**
     * 跳转到CVE详情页面
     */
    @PreAuthorize("@ss.hasPermi('security:cve:list')")
    @GetMapping("/detail/{cveId}")
    public String detail(@PathVariable("cveId") String cveId, ModelMap mmap)
    {
        CveInfo cveInfo = cveInfoService.selectCveInfoByCveId(cveId);
        mmap.put("cveInfo", cveInfo);
        return "security/cve/detail";
    }
}
