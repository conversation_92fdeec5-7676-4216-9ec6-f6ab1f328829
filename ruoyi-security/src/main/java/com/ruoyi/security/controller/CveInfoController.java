package com.ruoyi.security.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.security.domain.CveInfo;
import com.ruoyi.security.service.ICveInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * CVE漏洞信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/security/cve")
public class CveInfoController extends BaseController
{
    @Autowired
    private ICveInfoService cveInfoService;

    /**
     * 查询CVE漏洞信息列表
     */
    @PreAuthorize("@ss.hasPermi('security:cve:list')")
    @PostMapping("/list")
    public TableDataInfo list(CveInfo cveInfo)
    {
        startPage();
        List<CveInfo> list = cveInfoService.selectCveInfoList(cveInfo);
        return getDataTable(list);
    }

    /**
     * 导出CVE漏洞信息列表
     */
    @PreAuthorize("@ss.hasPermi('security:cve:export')")
    @Log(title = "CVE漏洞信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CveInfo cveInfo)
    {
        List<CveInfo> list = cveInfoService.selectCveInfoList(cveInfo);
        ExcelUtil<CveInfo> util = new ExcelUtil<CveInfo>(CveInfo.class);
        util.exportExcel(response, list, "CVE漏洞信息数据");
    }

    /**
     * 获取CVE漏洞信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('security:cve:query')")
    @GetMapping(value = "/{cveId}")
    public AjaxResult getInfo(@PathVariable("cveId") String cveId)
    {
        return success(cveInfoService.selectCveInfoByCveId(cveId));
    }

    /**
     * 新增CVE漏洞信息
     */
    @PreAuthorize("@ss.hasPermi('security:cve:add')")
    @Log(title = "CVE漏洞信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CveInfo cveInfo)
    {
        return toAjax(cveInfoService.insertCveInfo(cveInfo));
    }

    /**
     * 修改CVE漏洞信息
     */
    @PreAuthorize("@ss.hasPermi('security:cve:edit')")
    @Log(title = "CVE漏洞信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CveInfo cveInfo)
    {
        return toAjax(cveInfoService.updateCveInfo(cveInfo));
    }

    /**
     * 删除CVE漏洞信息
     */
    @PreAuthorize("@ss.hasPermi('security:cve:remove')")
    @Log(title = "CVE漏洞信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cveIds}")
    public AjaxResult remove(@PathVariable String[] cveIds)
    {
        return toAjax(cveInfoService.deleteCveInfoByCveIds(cveIds));
    }

    /**
     * 同步CVE数据
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @Log(title = "同步CVE数据", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public AjaxResult syncCveData()
    {
        try
        {
            boolean result = cveInfoService.syncCveData();
            if (result)
            {
                return success("CVE数据同步成功");
            }
            else
            {
                return error("CVE数据同步失败");
            }
        }
        catch (Exception e)
        {
            logger.error("同步CVE数据异常", e);
            return error("CVE数据同步异常: " + e.getMessage());
        }
    }

    /**
     * 根据组件查找CVE
     */
    @PreAuthorize("@ss.hasPermi('security:cve:query')")
    @GetMapping("/component/{component}")
    public AjaxResult findCveByComponent(@PathVariable("component") String component)
    {
        List<CveInfo> list = cveInfoService.findCveByComponent(component);
        return success(list);
    }
}
