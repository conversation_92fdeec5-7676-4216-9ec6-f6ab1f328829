package com.ruoyi.security.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.security.config.CveApiProperties;
import com.ruoyi.security.service.ICveSyncService;
import com.ruoyi.security.util.CveBulkImporter;

/**
 * CVE数据同步Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/security/cve/sync")
public class CveSyncController extends BaseController {

    @Autowired
    private ICveSyncService cveSyncService;

    @Autowired
    private CveApiProperties cveApiProperties;

    @Autowired
    private CveBulkImporter cveBulkImporter;

    /**
     * 检查API配置状态
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @GetMapping("/config")
    public AjaxResult checkConfig() {
        return cveSyncService.checkApiConfig();
    }



    /**
     * 同步CVE数据
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @Log(title = "CVE数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/start")
    public AjaxResult syncCveData(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        if (startDate != null && endDate != null) {
            return cveSyncService.syncCveData(startDate, endDate);
        } else {
            return cveSyncService.syncRecentCveData();
        }
    }

    /**
     * 获取同步状态
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @GetMapping("/status")
    public AjaxResult getSyncStatus() {
        return cveSyncService.getSyncStatus();
    }

    /**
     * 停止同步
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @Log(title = "停止CVE数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/stop")
    public AjaxResult stopSync() {
        return cveSyncService.stopSync();
    }

    /**
     * 批量导入指定年份的CVE数据
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @Log(title = "批量导入CVE数据", businessType = BusinessType.OTHER)
    @PostMapping("/bulk-import/year")
    public AjaxResult bulkImportByYear(@RequestParam int year) {
        try {
            // 异步执行批量导入
            new Thread(() -> {
                try {
                    cveBulkImporter.importCveDataByYear(year);
                } catch (Exception e) {
                    logger.error("批量导入{}年CVE数据失败", year, e);
                }
            }).start();

            return success("已开始批量导入" + year + "年的CVE数据，请稍后查看导入结果");
        } catch (Exception e) {
            logger.error("启动批量导入失败", e);
            return error("启动批量导入失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入最近指定天数的CVE数据
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @Log(title = "批量导入最近CVE数据", businessType = BusinessType.OTHER)
    @PostMapping("/bulk-import/recent")
    public AjaxResult bulkImportRecent(@RequestParam int days) {
        try {
            // 异步执行批量导入
            new Thread(() -> {
                try {
                    cveBulkImporter.importRecentCveData(days);
                } catch (Exception e) {
                    logger.error("批量导入最近{}天CVE数据失败", days, e);
                }
            }).start();

            return success("已开始批量导入最近" + days + "天的CVE数据，请稍后查看导入结果");
        } catch (Exception e) {
            logger.error("启动批量导入失败", e);
            return error("启动批量导入失败: " + e.getMessage());
        }
    }
}
