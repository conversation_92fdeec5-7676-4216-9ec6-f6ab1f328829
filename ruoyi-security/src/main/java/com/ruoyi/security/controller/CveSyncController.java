package com.ruoyi.security.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.security.config.CveApiProperties;
import com.ruoyi.security.service.ICveSyncService;

/**
 * CVE数据同步Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/security/cve/sync")
public class CveSyncController extends BaseController {

    @Autowired
    private ICveSyncService cveSyncService;

    @Autowired
    private CveApiProperties cveApiProperties;

    /**
     * 检查API配置状态
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @GetMapping("/config")
    public AjaxResult checkConfig() {
        return cveSyncService.checkApiConfig();
    }

    /**
     * 获取API配置信息
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @GetMapping("/config/info")
    public AjaxResult getConfigInfo() {
        CveApiProperties.NvdConfig nvdConfig = cveApiProperties.getNvd();
        return AjaxResult.success()
                .put("baseUrl", nvdConfig.getBaseUrl())
                .put("timeout", nvdConfig.getTimeout())
                .put("requestInterval", nvdConfig.getRequestInterval())
                .put("resultsPerPage", nvdConfig.getResultsPerPage())
                .put("apiKeyConfigured", nvdConfig.isApiKeyConfigured());
    }

    /**
     * 同步CVE数据
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @Log(title = "CVE数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/start")
    public AjaxResult syncCveData(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        if (startDate != null && endDate != null) {
            return cveSyncService.syncCveData(startDate, endDate);
        } else {
            return cveSyncService.syncRecentCveData();
        }
    }

    /**
     * 获取同步状态
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @GetMapping("/status")
    public AjaxResult getSyncStatus() {
        return cveSyncService.getSyncStatus();
    }

    /**
     * 停止同步
     */
    @PreAuthorize("@ss.hasPermi('security:cve:sync')")
    @Log(title = "停止CVE数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/stop")
    public AjaxResult stopSync() {
        return cveSyncService.stopSync();
    }
}
