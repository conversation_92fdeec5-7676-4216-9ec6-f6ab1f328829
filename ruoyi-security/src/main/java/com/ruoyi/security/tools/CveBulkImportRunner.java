package com.ruoyi.security.tools;

import com.ruoyi.security.util.CveBulkImporter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * CVE批量导入命令行工具
 *
 * 这是一个独立的命令行工具，不会在主应用启动时自动执行。
 *
 * 使用方法：
 * 1. 导入最近30天数据：
 *    mvn spring-boot:run -Dspring-boot.run.main-class=com.ruoyi.security.CveBulkImportRunner -Dspring-boot.run.arguments="--import.recent=30"
 * 2. 导入2024年数据：
 *    mvn spring-boot:run -Dspring-boot.run.main-class=com.ruoyi.security.CveBulkImportRunner -Dspring-boot.run.arguments="--import.year=2024"
 * 3. 导入2023年数据：
 *    mvn spring-boot:run -Dspring-boot.run.main-class=com.ruoyi.security.CveBulkImportRunner -Dspring-boot.run.arguments="--import.year=2023"
 */
@SpringBootApplication(scanBasePackages = {"com.ruoyi"})
public class CveBulkImportRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(CveBulkImportRunner.class);

    @Autowired
    private CveBulkImporter cveBulkImporter;

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "dev");
        ConfigurableApplicationContext context = SpringApplication.run(CveBulkImportRunner.class, args);
        
        // 程序执行完成后关闭Spring上下文
        context.close();
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("CVE批量导入工具启动");
        
        boolean hasTask = false;
        
        // 解析命令行参数
        for (String arg : args) {
            if (arg.startsWith("--import.recent=")) {
                String daysStr = arg.substring("--import.recent=".length());
                try {
                    int days = Integer.parseInt(daysStr);
                    if (days > 0 && days <= 365) {
                        logger.info("开始导入最近{}天的CVE数据", days);
                        cveBulkImporter.importRecentCveData(days);
                        hasTask = true;
                    } else {
                        logger.error("天数参数无效，必须在1-365之间: {}", days);
                    }
                } catch (NumberFormatException e) {
                    logger.error("天数参数格式错误: {}", daysStr);
                }
            } else if (arg.startsWith("--import.year=")) {
                String yearStr = arg.substring("--import.year=".length());
                try {
                    int year = Integer.parseInt(yearStr);
                    if (year >= 2020 && year <= 2025) {
                        logger.info("开始导入{}年的CVE数据", year);
                        cveBulkImporter.importCveDataByYear(year);
                        hasTask = true;
                    } else {
                        logger.error("年份参数无效，必须在2020-2025之间: {}", year);
                    }
                } catch (NumberFormatException e) {
                    logger.error("年份参数格式错误: {}", yearStr);
                }
            }
        }
        
        if (!hasTask) {
            logger.info("未指定导入任务，使用默认参数导入最近30天数据");
            logger.info("使用方法：");
            logger.info("  导入最近N天数据：--import.recent=N");
            logger.info("  导入指定年份数据：--import.year=YYYY");
            logger.info("  示例：--import.recent=30 或 --import.year=2024");
            
            // 默认导入最近30天数据
            cveBulkImporter.importRecentCveData(30);
        }
        
        logger.info("CVE批量导入工具执行完成");
    }
}
