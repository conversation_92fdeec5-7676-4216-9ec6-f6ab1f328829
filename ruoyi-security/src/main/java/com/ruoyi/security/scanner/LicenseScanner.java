package com.ruoyi.security.scanner;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.system.service.ISysDictDataService;

/**
 * 许可证扫描器
 * 负责检查Maven依赖的许可证类型和风险等级
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
public class LicenseScanner
{
    private static final Logger log = LoggerFactory.getLogger(LicenseScanner.class);

    @Autowired
    private ISysDictDataService dictDataService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    // 许可证风险等级映射
    private static final Map<String, String> LICENSE_RISK_MAP = new HashMap<>();
    
    static
    {
        // 高风险许可证 (GPL/AGPL等传染性许可证)
        LICENSE_RISK_MAP.put("GPL-2.0", "3");
        LICENSE_RISK_MAP.put("GPL-3.0", "3");
        LICENSE_RISK_MAP.put("AGPL-3.0", "3");
        LICENSE_RISK_MAP.put("GPL-2.0-only", "3");
        LICENSE_RISK_MAP.put("GPL-3.0-only", "3");
        LICENSE_RISK_MAP.put("GPL-2.0-or-later", "3");
        LICENSE_RISK_MAP.put("GPL-3.0-or-later", "3");
        
        // 中风险许可证 (LGPL等)
        LICENSE_RISK_MAP.put("LGPL-2.1", "2");
        LICENSE_RISK_MAP.put("LGPL-3.0", "2");
        LICENSE_RISK_MAP.put("LGPL-2.1-only", "2");
        LICENSE_RISK_MAP.put("LGPL-3.0-only", "2");
        LICENSE_RISK_MAP.put("LGPL-2.1-or-later", "2");
        LICENSE_RISK_MAP.put("LGPL-3.0-or-later", "2");
        
        // 低风险许可证 (Apache, MIT, BSD等)
        LICENSE_RISK_MAP.put("Apache-2.0", "1");
        LICENSE_RISK_MAP.put("MIT", "1");
        LICENSE_RISK_MAP.put("BSD-2-Clause", "1");
        LICENSE_RISK_MAP.put("BSD-3-Clause", "1");
        LICENSE_RISK_MAP.put("ISC", "1");
        LICENSE_RISK_MAP.put("Unlicense", "1");
    }

    /**
     * 扫描依赖的许可证信息
     * 
     * @param dependencies 依赖列表
     */
    public void scanLicenses(List<ProjectDependency> dependencies)
    {
        for (ProjectDependency dependency : dependencies)
        {
            try
            {
                scanSingleDependencyLicense(dependency);
                Thread.sleep(100); // 避免请求过于频繁
            }
            catch (Exception e)
            {
                log.error("扫描依赖许可证失败: {}:{}", dependency.getGroupId(), dependency.getArtifactId(), e);
            }
        }
    }

    /**
     * 扫描单个依赖的许可证信息
     * 
     * @param dependency 依赖
     */
    private void scanSingleDependencyLicense(ProjectDependency dependency)
    {
        String license = getLicenseFromMavenCentral(dependency);
        
        if (license != null && !license.isEmpty())
        {
            dependency.setLicenseType(license);
            
            // 设置风险等级
            String riskLevel = LICENSE_RISK_MAP.getOrDefault(license, "1"); // 默认低风险
            dependency.setLicenseRiskLevel(riskLevel);
            
            log.debug("依赖 {}:{} 许可证: {}, 风险等级: {}", 
                dependency.getGroupId(), dependency.getArtifactId(), license, riskLevel);
        }
        else
        {
            dependency.setLicenseType("Unknown");
            dependency.setLicenseRiskLevel("2"); // 未知许可证设为中风险
        }
    }

    /**
     * 从Maven中央仓库获取许可证信息
     * 
     * @param dependency 依赖
     * @return 许可证类型
     */
    private String getLicenseFromMavenCentral(ProjectDependency dependency)
    {
        try
        {
            String url = String.format("https://search.maven.org/solrsearch/select?q=g:%s+AND+a:%s&rows=1&wt=json",
                dependency.getGroupId(), dependency.getArtifactId());
            
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("User-Agent", "RuoYi-Security-Scanner/1.0");
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet))
            {
                HttpEntity entity = response.getEntity();
                if (entity != null)
                {
                    String jsonResponse = EntityUtils.toString(entity);
                    return parseLicenseFromResponse(jsonResponse);
                }
            }
        }
        catch (IOException e)
        {
            log.debug("从Maven中央仓库获取许可证信息失败: {}:{}", dependency.getGroupId(), dependency.getArtifactId());
        }
        
        return null;
    }

    /**
     * 解析Maven中央仓库响应中的许可证信息
     * 
     * @param jsonResponse JSON响应
     * @return 许可证类型
     */
    private String parseLicenseFromResponse(String jsonResponse)
    {
        try
        {
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode response = root.get("response");
            
            if (response != null && response.get("numFound").asInt() > 0)
            {
                JsonNode docs = response.get("docs");
                if (docs != null && docs.isArray() && docs.size() > 0)
                {
                    JsonNode doc = docs.get(0);
                    JsonNode licenseUrls = doc.get("licenseUrls");
                    
                    if (licenseUrls != null && licenseUrls.isArray() && licenseUrls.size() > 0)
                    {
                        String licenseUrl = licenseUrls.get(0).asText();
                        return extractLicenseFromUrl(licenseUrl);
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.debug("解析许可证响应失败", e);
        }
        
        return null;
    }

    /**
     * 从许可证URL中提取许可证类型
     * 
     * @param licenseUrl 许可证URL
     * @return 许可证类型
     */
    private String extractLicenseFromUrl(String licenseUrl)
    {
        if (licenseUrl.contains("apache.org/licenses/LICENSE-2.0"))
        {
            return "Apache-2.0";
        }
        else if (licenseUrl.contains("opensource.org/licenses/MIT"))
        {
            return "MIT";
        }
        else if (licenseUrl.contains("gnu.org/licenses/gpl-2.0"))
        {
            return "GPL-2.0";
        }
        else if (licenseUrl.contains("gnu.org/licenses/gpl-3.0"))
        {
            return "GPL-3.0";
        }
        else if (licenseUrl.contains("gnu.org/licenses/agpl-3.0"))
        {
            return "AGPL-3.0";
        }
        else if (licenseUrl.contains("gnu.org/licenses/lgpl-2.1"))
        {
            return "LGPL-2.1";
        }
        else if (licenseUrl.contains("gnu.org/licenses/lgpl-3.0"))
        {
            return "LGPL-3.0";
        }
        else if (licenseUrl.contains("opensource.org/licenses/BSD-3-Clause"))
        {
            return "BSD-3-Clause";
        }
        else if (licenseUrl.contains("opensource.org/licenses/BSD-2-Clause"))
        {
            return "BSD-2-Clause";
        }
        
        return "Other";
    }
}
