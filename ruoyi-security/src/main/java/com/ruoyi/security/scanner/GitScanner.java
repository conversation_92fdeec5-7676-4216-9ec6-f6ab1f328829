package com.ruoyi.security.scanner;

import java.io.File;
import java.io.IOException;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.ruoyi.security.domain.ProjectInfo;

/**
 * Git扫描器
 * 负责从GitLab拉取项目代码
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
public class GitScanner
{
    private static final Logger log = LoggerFactory.getLogger(GitScanner.class);

    /**
     * 克隆或更新Git仓库
     * 
     * @param projectInfo 项目信息
     * @param localPath 本地路径
     * @return 是否成功
     */
    public boolean cloneOrUpdateRepository(ProjectInfo projectInfo, String localPath)
    {
        try
        {
            File localDir = new File(localPath);
            
            // 如果本地目录已存在，先删除
            if (localDir.exists())
            {
                deleteDirectory(localDir);
            }
            
            // 创建本地目录
            localDir.mkdirs();
            
            // 克隆仓库
            Git git = Git.cloneRepository()
                .setURI(projectInfo.getGitUrl())
                .setDirectory(localDir)
                .setBranch(projectInfo.getGitBranch())
                .setCredentialsProvider(new UsernamePasswordCredentialsProvider(
                    projectInfo.getGitUsername(), 
                    projectInfo.getGitPassword()))
                .call();
            
            git.close();
            log.info("成功克隆Git仓库: {} 到本地路径: {}", projectInfo.getGitUrl(), localPath);
            return true;
        }
        catch (GitAPIException e)
        {
            log.error("克隆Git仓库失败: {}", e.getMessage(), e);
            return false;
        }
        catch (Exception e)
        {
            log.error("克隆Git仓库失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 递归删除目录
     * 
     * @param directory 目录
     */
    private void deleteDirectory(File directory)
    {
        if (directory.exists())
        {
            File[] files = directory.listFiles();
            if (files != null)
            {
                for (File file : files)
                {
                    if (file.isDirectory())
                    {
                        deleteDirectory(file);
                    }
                    else
                    {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }

    /**
     * 清理本地仓库
     * 
     * @param localPath 本地路径
     */
    public void cleanupRepository(String localPath)
    {
        File localDir = new File(localPath);
        if (localDir.exists())
        {
            deleteDirectory(localDir);
            log.info("清理本地仓库: {}", localPath);
        }
    }
}
