package com.ruoyi.security.scanner;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.apache.maven.model.Dependency;
import org.apache.maven.model.Model;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;
import org.codehaus.plexus.util.xml.pull.XmlPullParserException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.ruoyi.security.domain.ProjectDependency;

/**
 * Maven扫描器
 * 负责解析pom.xml文件，提取Maven依赖信息
 * 支持单模块和多模块项目
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
public class MavenScanner
{
    private static final Logger log = LoggerFactory.getLogger(MavenScanner.class);

    /**
     * 扫描项目依赖
     * 
     * @param projectPath 项目路径
     * @param projectId 项目ID
     * @param taskId 任务ID
     * @return 依赖列表
     */
    public List<ProjectDependency> scanDependencies(String projectPath, Long projectId, Long taskId)
    {
        List<ProjectDependency> dependencies = new ArrayList<>();
        
        try
        {
            File projectDir = new File(projectPath);
            
            // 查找所有pom.xml文件
            List<File> pomFiles = findPomFiles(projectDir);
            
            for (File pomFile : pomFiles)
            {
                log.info("解析pom文件: {}", pomFile.getAbsolutePath());
                List<ProjectDependency> pomDependencies = parsePomFile(pomFile, projectId, taskId);
                dependencies.addAll(pomDependencies);
            }
            
            // 去重处理
            dependencies = removeDuplicates(dependencies);
            
            log.info("共扫描到 {} 个依赖", dependencies.size());
        }
        catch (Exception e)
        {
            log.error("扫描Maven依赖失败: {}", e.getMessage(), e);
        }
        
        return dependencies;
    }

    /**
     * 递归查找所有pom.xml文件
     * 
     * @param directory 目录
     * @return pom文件列表
     */
    private List<File> findPomFiles(File directory)
    {
        List<File> pomFiles = new ArrayList<>();
        
        if (directory.isDirectory())
        {
            File[] files = directory.listFiles();
            if (files != null)
            {
                for (File file : files)
                {
                    if (file.isFile() && "pom.xml".equals(file.getName()))
                    {
                        pomFiles.add(file);
                    }
                    else if (file.isDirectory() && !file.getName().startsWith("."))
                    {
                        // 递归查找子目录，但跳过隐藏目录
                        pomFiles.addAll(findPomFiles(file));
                    }
                }
            }
        }
        
        return pomFiles;
    }

    /**
     * 解析单个pom.xml文件
     * 
     * @param pomFile pom文件
     * @param projectId 项目ID
     * @param taskId 任务ID
     * @return 依赖列表
     */
    private List<ProjectDependency> parsePomFile(File pomFile, Long projectId, Long taskId)
    {
        List<ProjectDependency> dependencies = new ArrayList<>();
        
        try (FileReader reader = new FileReader(pomFile))
        {
            MavenXpp3Reader mavenReader = new MavenXpp3Reader();
            Model model = mavenReader.read(reader);
            
            // 解析直接依赖
            List<Dependency> deps = model.getDependencies();
            if (deps != null)
            {
                for (Dependency dep : deps)
                {
                    // 跳过test scope的依赖
                    if ("test".equals(dep.getScope()))
                    {
                        continue;
                    }
                    
                    ProjectDependency projectDep = new ProjectDependency();
                    projectDep.setProjectId(projectId);
                    projectDep.setTaskId(taskId);
                    projectDep.setGroupId(dep.getGroupId());
                    projectDep.setArtifactId(dep.getArtifactId());
                    projectDep.setVersion(dep.getVersion());
                    
                    dependencies.add(projectDep);
                }
            }
        }
        catch (IOException | XmlPullParserException e)
        {
            log.error("解析pom文件失败: {}", pomFile.getAbsolutePath(), e);
        }
        
        return dependencies;
    }

    /**
     * 去重处理
     * 
     * @param dependencies 依赖列表
     * @return 去重后的依赖列表
     */
    private List<ProjectDependency> removeDuplicates(List<ProjectDependency> dependencies)
    {
        List<ProjectDependency> uniqueDependencies = new ArrayList<>();
        
        for (ProjectDependency dep : dependencies)
        {
            boolean exists = false;
            for (ProjectDependency unique : uniqueDependencies)
            {
                if (dep.getGroupId().equals(unique.getGroupId()) && 
                    dep.getArtifactId().equals(unique.getArtifactId()))
                {
                    exists = true;
                    break;
                }
            }
            
            if (!exists)
            {
                uniqueDependencies.add(dep);
            }
        }
        
        return uniqueDependencies;
    }
}
