package com.ruoyi.security.mapper;

import java.util.List;
import com.ruoyi.security.domain.LicenseInfo;

/**
 * 许可证信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface LicenseInfoMapper 
{
    /**
     * 查询许可证信息
     * 
     * @param licenseId 许可证信息主键
     * @return 许可证信息
     */
    public LicenseInfo selectLicenseInfoByLicenseId(Long licenseId);

    /**
     * 查询许可证信息列表
     * 
     * @param licenseInfo 许可证信息
     * @return 许可证信息集合
     */
    public List<LicenseInfo> selectLicenseInfoList(LicenseInfo licenseInfo);

    /**
     * 新增许可证信息
     * 
     * @param licenseInfo 许可证信息
     * @return 结果
     */
    public int insertLicenseInfo(LicenseInfo licenseInfo);

    /**
     * 修改许可证信息
     * 
     * @param licenseInfo 许可证信息
     * @return 结果
     */
    public int updateLicenseInfo(LicenseInfo licenseInfo);

    /**
     * 删除许可证信息
     * 
     * @param licenseId 许可证信息主键
     * @return 结果
     */
    public int deleteLicenseInfoByLicenseId(Long licenseId);

    /**
     * 批量删除许可证信息
     * 
     * @param licenseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLicenseInfoByLicenseIds(Long[] licenseIds);

    /**
     * 根据许可证类型查询许可证信息
     * 
     * @param licenseType 许可证类型
     * @return 许可证信息
     */
    public LicenseInfo selectLicenseInfoByType(String licenseType);

    /**
     * 校验许可证名称是否唯一
     * 
     * @param licenseName 许可证名称
     * @return 许可证信息
     */
    public LicenseInfo checkLicenseNameUnique(String licenseName);
}
