package com.ruoyi.security.mapper;

import java.util.List;
import com.ruoyi.security.domain.ScanReport;

/**
 * 扫描报告Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ScanReportMapper 
{
    /**
     * 查询扫描报告
     * 
     * @param reportId 扫描报告主键
     * @return 扫描报告
     */
    public ScanReport selectScanReportByReportId(Long reportId);

    /**
     * 查询扫描报告列表
     * 
     * @param scanReport 扫描报告
     * @return 扫描报告集合
     */
    public List<ScanReport> selectScanReportList(ScanReport scanReport);

    /**
     * 新增扫描报告
     * 
     * @param scanReport 扫描报告
     * @return 结果
     */
    public int insertScanReport(ScanReport scanReport);

    /**
     * 修改扫描报告
     * 
     * @param scanReport 扫描报告
     * @return 结果
     */
    public int updateScanReport(ScanReport scanReport);

    /**
     * 删除扫描报告
     * 
     * @param reportId 扫描报告主键
     * @return 结果
     */
    public int deleteScanReportByReportId(Long reportId);

    /**
     * 批量删除扫描报告
     * 
     * @param reportIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScanReportByReportIds(Long[] reportIds);

    /**
     * 根据项目名模糊查询报告
     * 
     * @param projectName 项目名称
     * @return 报告列表
     */
    public List<ScanReport> selectScanReportByProjectName(String projectName);
}
