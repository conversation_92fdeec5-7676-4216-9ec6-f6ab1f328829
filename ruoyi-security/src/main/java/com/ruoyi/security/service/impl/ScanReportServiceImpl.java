package com.ruoyi.security.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.security.mapper.ScanReportMapper;
import com.ruoyi.security.domain.ScanReport;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.security.service.IScanReportService;

/**
 * 扫描报告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class ScanReportServiceImpl implements IScanReportService 
{
    private static final Logger log = LoggerFactory.getLogger(ScanReportServiceImpl.class);

    @Autowired
    private ScanReportMapper scanReportMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 查询扫描报告
     * 
     * @param reportId 扫描报告主键
     * @return 扫描报告
     */
    @Override
    public ScanReport selectScanReportByReportId(Long reportId)
    {
        return scanReportMapper.selectScanReportByReportId(reportId);
    }

    /**
     * 查询扫描报告列表
     * 
     * @param scanReport 扫描报告
     * @return 扫描报告
     */
    @Override
    public List<ScanReport> selectScanReportList(ScanReport scanReport)
    {
        return scanReportMapper.selectScanReportList(scanReport);
    }

    /**
     * 新增扫描报告
     * 
     * @param scanReport 扫描报告
     * @return 结果
     */
    @Override
    public int insertScanReport(ScanReport scanReport)
    {
        scanReport.setCreateTime(new Date());
        scanReport.setCreateBy(SecurityUtils.getUsername());
        return scanReportMapper.insertScanReport(scanReport);
    }

    /**
     * 修改扫描报告
     * 
     * @param scanReport 扫描报告
     * @return 结果
     */
    @Override
    public int updateScanReport(ScanReport scanReport)
    {
        scanReport.setUpdateTime(new Date());
        scanReport.setUpdateBy(SecurityUtils.getUsername());
        return scanReportMapper.updateScanReport(scanReport);
    }

    /**
     * 批量删除扫描报告
     * 
     * @param reportIds 需要删除的扫描报告主键
     * @return 结果
     */
    @Override
    public int deleteScanReportByReportIds(Long[] reportIds)
    {
        return scanReportMapper.deleteScanReportByReportIds(reportIds);
    }

    /**
     * 删除扫描报告信息
     * 
     * @param reportId 扫描报告主键
     * @return 结果
     */
    @Override
    public int deleteScanReportByReportId(Long reportId)
    {
        return scanReportMapper.deleteScanReportByReportId(reportId);
    }

    /**
     * 生成扫描报告
     * 
     * @param taskId 任务ID
     * @param projectInfo 项目信息
     * @param dependencies 依赖列表
     * @return 结果
     */
    @Override
    public boolean generateReport(Long taskId, ProjectInfo projectInfo, List<ProjectDependency> dependencies)
    {
        try
        {
            log.info("开始生成扫描报告，任务ID: {}, 项目: {}", taskId, projectInfo.getProjectName());
            
            // 构建报告内容
            Map<String, Object> reportContent = buildReportContent(projectInfo, dependencies);
            
            // 创建扫描报告
            ScanReport scanReport = new ScanReport();
            scanReport.setTaskId(taskId);
            scanReport.setProjectId(projectInfo.getProjectId());
            scanReport.setProjectName(projectInfo.getProjectName());
            scanReport.setReportTitle("安全扫描报告-" + projectInfo.getProjectName());
            
            // 将报告内容转换为JSON
            String reportJson = objectMapper.writeValueAsString(reportContent);
            scanReport.setReportContent(reportJson);
            
            // 统计信息
            scanReport.setTotalDependencies((long) dependencies.size());
            
            long cveCount = dependencies.stream()
                .filter(dep -> "1".equals(dep.getHasCve()))
                .mapToLong(dep -> dep.getCveCount() != null ? dep.getCveCount() : 0)
                .sum();
            scanReport.setCveCount(cveCount);
            
            long highRiskLicenseCount = dependencies.stream()
                .filter(dep -> "3".equals(dep.getLicenseRiskLevel()))
                .count();
            scanReport.setHighRiskLicenseCount(highRiskLicenseCount);
            
            // 保存报告
            int result = insertScanReport(scanReport);
            
            log.info("扫描报告生成完成，报告ID: {}", scanReport.getReportId());
            return result > 0;
        }
        catch (Exception e)
        {
            log.error("生成扫描报告失败", e);
            return false;
        }
    }

    /**
     * 构建报告内容
     * 
     * @param projectInfo 项目信息
     * @param dependencies 依赖列表
     * @return 报告内容
     */
    private Map<String, Object> buildReportContent(ProjectInfo projectInfo, List<ProjectDependency> dependencies)
    {
        Map<String, Object> content = new HashMap<>();
        
        // 项目基本信息
        Map<String, Object> projectSummary = new HashMap<>();
        projectSummary.put("projectName", projectInfo.getProjectName());
        projectSummary.put("projectCode", projectInfo.getProjectCode());
        projectSummary.put("gitUrl", projectInfo.getGitUrl());
        projectSummary.put("gitBranch", projectInfo.getGitBranch());
        projectSummary.put("scanTime", new Date());
        content.put("projectSummary", projectSummary);
        
        // 扫描统计
        Map<String, Object> scanStatistics = new HashMap<>();
        scanStatistics.put("totalDependencies", dependencies.size());
        
        // CVE统计
        long cveHighCount = 0, cveMediumCount = 0, cveLowCount = 0, cveCriticalCount = 0;
        long cveAffectedDependencies = 0;
        
        // 许可证统计
        long licenseHighRiskCount = 0, licenseMediumRiskCount = 0, licenseLowRiskCount = 0;
        
        for (ProjectDependency dep : dependencies)
        {
            // CVE统计
            if ("1".equals(dep.getHasCve()))
            {
                cveAffectedDependencies++;
                if (dep.getMaxCvssScore() != null)
                {
                    double cvssScore = dep.getMaxCvssScore().doubleValue();
                    if (cvssScore >= 9.0) cveCriticalCount++;
                    else if (cvssScore >= 7.0) cveHighCount++;
                    else if (cvssScore >= 4.0) cveMediumCount++;
                    else cveLowCount++;
                }
            }
            
            // 许可证统计
            String riskLevel = dep.getLicenseRiskLevel();
            if ("3".equals(riskLevel)) licenseHighRiskCount++;
            else if ("2".equals(riskLevel)) licenseMediumRiskCount++;
            else licenseLowRiskCount++;
        }
        
        Map<String, Object> cveStats = new HashMap<>();
        cveStats.put("affectedDependencies", cveAffectedDependencies);
        cveStats.put("criticalCount", cveCriticalCount);
        cveStats.put("highCount", cveHighCount);
        cveStats.put("mediumCount", cveMediumCount);
        cveStats.put("lowCount", cveLowCount);
        scanStatistics.put("cveStatistics", cveStats);
        
        Map<String, Object> licenseStats = new HashMap<>();
        licenseStats.put("highRiskCount", licenseHighRiskCount);
        licenseStats.put("mediumRiskCount", licenseMediumRiskCount);
        licenseStats.put("lowRiskCount", licenseLowRiskCount);
        scanStatistics.put("licenseStatistics", licenseStats);
        
        content.put("scanStatistics", scanStatistics);
        
        // 高风险依赖详情
        List<ProjectDependency> highRiskDependencies = dependencies.stream()
            .filter(dep -> isHighRiskDependency(dep))
            .collect(Collectors.toList());
        content.put("highRiskDependencies", highRiskDependencies);
        
        // 所有依赖详情
        content.put("allDependencies", dependencies);
        
        return content;
    }

    /**
     * 判断是否为高风险依赖
     * 
     * @param dependency 依赖
     * @return 是否高风险
     */
    private boolean isHighRiskDependency(ProjectDependency dependency)
    {
        // CVE高风险
        if ("1".equals(dependency.getHasCve()) && dependency.getMaxCvssScore() != null)
        {
            double cvssScore = dependency.getMaxCvssScore().doubleValue();
            if (cvssScore >= 7.0) return true;
        }
        
        // 许可证高风险
        if ("3".equals(dependency.getLicenseRiskLevel()))
        {
            return true;
        }
        
        return false;
    }

    /**
     * 根据项目名模糊查询报告
     * 
     * @param projectName 项目名称
     * @return 报告列表
     */
    @Override
    public List<ScanReport> selectScanReportByProjectName(String projectName)
    {
        return scanReportMapper.selectScanReportByProjectName(projectName);
    }
}
