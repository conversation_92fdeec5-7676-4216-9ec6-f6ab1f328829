package com.ruoyi.security.service;

import java.util.List;
import com.ruoyi.security.domain.ScanReport;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.domain.ProjectDependency;

/**
 * 扫描报告Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IScanReportService 
{
    /**
     * 查询扫描报告
     * 
     * @param reportId 扫描报告主键
     * @return 扫描报告
     */
    public ScanReport selectScanReportByReportId(Long reportId);

    /**
     * 查询扫描报告列表
     * 
     * @param scanReport 扫描报告
     * @return 扫描报告集合
     */
    public List<ScanReport> selectScanReportList(ScanReport scanReport);

    /**
     * 新增扫描报告
     * 
     * @param scanReport 扫描报告
     * @return 结果
     */
    public int insertScanReport(ScanReport scanReport);

    /**
     * 修改扫描报告
     * 
     * @param scanReport 扫描报告
     * @return 结果
     */
    public int updateScanReport(ScanReport scanReport);

    /**
     * 批量删除扫描报告
     * 
     * @param reportIds 需要删除的扫描报告主键集合
     * @return 结果
     */
    public int deleteScanReportByReportIds(Long[] reportIds);

    /**
     * 删除扫描报告信息
     * 
     * @param reportId 扫描报告主键
     * @return 结果
     */
    public int deleteScanReportByReportId(Long reportId);

    /**
     * 生成扫描报告
     * 
     * @param taskId 任务ID
     * @param projectInfo 项目信息
     * @param dependencies 依赖列表
     * @return 结果
     */
    public boolean generateReport(Long taskId, ProjectInfo projectInfo, List<ProjectDependency> dependencies);

    /**
     * 根据项目名模糊查询报告
     * 
     * @param projectName 项目名称
     * @return 报告列表
     */
    public List<ScanReport> selectScanReportByProjectName(String projectName);
}
