package com.ruoyi.security.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.security.mapper.ProjectInfoMapper;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.service.IProjectInfoService;

/**
 * 项目信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class ProjectInfoServiceImpl implements IProjectInfoService 
{
    private static final Logger log = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    /**
     * 查询项目信息
     * 
     * @param projectId 项目信息主键
     * @return 项目信息
     */
    @Override
    public ProjectInfo selectProjectInfoByProjectId(Long projectId)
    {
        return projectInfoMapper.selectProjectInfoByProjectId(projectId);
    }

    /**
     * 查询项目信息列表
     * 
     * @param projectInfo 项目信息
     * @return 项目信息
     */
    @Override
    public List<ProjectInfo> selectProjectInfoList(ProjectInfo projectInfo)
    {
        return projectInfoMapper.selectProjectInfoList(projectInfo);
    }

    /**
     * 新增项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    @Override
    public int insertProjectInfo(ProjectInfo projectInfo)
    {
        projectInfo.setCreateTime(DateUtils.getNowDate());
        projectInfo.setDelFlag("0");
        // 设置默认扫描状态为未扫描
        if (projectInfo.getScanStatus() == null) {
            projectInfo.setScanStatus("0");
        }
        // 设置默认状态为正常
        if (projectInfo.getStatus() == null) {
            projectInfo.setStatus("0");
        }
        return projectInfoMapper.insertProjectInfo(projectInfo);
    }

    /**
     * 修改项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    @Override
    public int updateProjectInfo(ProjectInfo projectInfo)
    {
        projectInfo.setUpdateTime(DateUtils.getNowDate());
        return projectInfoMapper.updateProjectInfo(projectInfo);
    }

    /**
     * 批量删除项目信息
     * 
     * @param projectIds 需要删除的项目信息主键
     * @return 结果
     */
    @Override
    public int deleteProjectInfoByProjectIds(Long[] projectIds)
    {
        return projectInfoMapper.deleteProjectInfoByProjectIds(projectIds);
    }

    /**
     * 删除项目信息信息
     * 
     * @param projectId 项目信息主键
     * @return 结果
     */
    @Override
    public int deleteProjectInfoByProjectId(Long projectId)
    {
        return projectInfoMapper.deleteProjectInfoByProjectId(projectId);
    }

    /**
     * 校验项目编码是否唯一
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    @Override
    public boolean checkProjectCodeUnique(ProjectInfo projectInfo)
    {
        Long projectId = StringUtils.isNull(projectInfo.getProjectId()) ? -1L : projectInfo.getProjectId();
        ProjectInfo info = projectInfoMapper.checkProjectCodeUnique(projectInfo.getProjectCode());
        if (StringUtils.isNotNull(info) && info.getProjectId().longValue() != projectId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 测试Git连接
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    @Override
    public boolean testGitConnection(ProjectInfo projectInfo)
    {
        try
        {
            // 使用JGit测试连接
            Git.lsRemoteRepository()
                .setRemote(projectInfo.getGitUrl())
                .setCredentialsProvider(new UsernamePasswordCredentialsProvider(
                    projectInfo.getGitUsername(), 
                    projectInfo.getGitPassword()))
                .call();
            return true;
        }
        catch (Exception e)
        {
            log.error("Git连接测试失败: {}", e.getMessage());
            return false;
        }
    }
}
