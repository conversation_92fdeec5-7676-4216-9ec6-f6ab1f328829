package com.ruoyi.security.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.security.mapper.ProjectDependencyMapper;
import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.security.service.IProjectDependencyService;

/**
 * 项目依赖Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class ProjectDependencyServiceImpl implements IProjectDependencyService 
{
    @Autowired
    private ProjectDependencyMapper projectDependencyMapper;

    /**
     * 查询项目依赖
     * 
     * @param dependencyId 项目依赖主键
     * @return 项目依赖
     */
    @Override
    public ProjectDependency selectProjectDependencyByDependencyId(Long dependencyId)
    {
        return projectDependencyMapper.selectProjectDependencyByDependencyId(dependencyId);
    }

    /**
     * 查询项目依赖列表
     * 
     * @param projectDependency 项目依赖
     * @return 项目依赖
     */
    @Override
    public List<ProjectDependency> selectProjectDependencyList(ProjectDependency projectDependency)
    {
        return projectDependencyMapper.selectProjectDependencyList(projectDependency);
    }

    /**
     * 新增项目依赖
     * 
     * @param projectDependency 项目依赖
     * @return 结果
     */
    @Override
    public int insertProjectDependency(ProjectDependency projectDependency)
    {
        projectDependency.setCreateTime(new Date());
        projectDependency.setCreateBy(SecurityUtils.getUsername());
        return projectDependencyMapper.insertProjectDependency(projectDependency);
    }

    /**
     * 修改项目依赖
     * 
     * @param projectDependency 项目依赖
     * @return 结果
     */
    @Override
    public int updateProjectDependency(ProjectDependency projectDependency)
    {
        projectDependency.setUpdateTime(new Date());
        projectDependency.setUpdateBy(SecurityUtils.getUsername());
        return projectDependencyMapper.updateProjectDependency(projectDependency);
    }

    /**
     * 批量删除项目依赖
     * 
     * @param dependencyIds 需要删除的项目依赖主键
     * @return 结果
     */
    @Override
    public int deleteProjectDependencyByDependencyIds(Long[] dependencyIds)
    {
        return projectDependencyMapper.deleteProjectDependencyByDependencyIds(dependencyIds);
    }

    /**
     * 删除项目依赖信息
     * 
     * @param dependencyId 项目依赖主键
     * @return 结果
     */
    @Override
    public int deleteProjectDependencyByDependencyId(Long dependencyId)
    {
        return projectDependencyMapper.deleteProjectDependencyByDependencyId(dependencyId);
    }

    /**
     * 批量插入项目依赖
     * 
     * @param dependencies 项目依赖列表
     * @return 结果
     */
    @Override
    public int batchInsertProjectDependency(List<ProjectDependency> dependencies)
    {
        if (dependencies == null || dependencies.isEmpty())
        {
            return 0;
        }
        
        // 设置创建信息
        String username = SecurityUtils.getUsername();
        Date now = new Date();
        for (ProjectDependency dependency : dependencies)
        {
            dependency.setCreateTime(now);
            dependency.setCreateBy(username);
        }
        
        return projectDependencyMapper.batchInsertProjectDependency(dependencies);
    }

    /**
     * 根据任务ID查询依赖列表
     * 
     * @param taskId 任务ID
     * @return 依赖列表
     */
    @Override
    public List<ProjectDependency> selectProjectDependencyByTaskId(Long taskId)
    {
        return projectDependencyMapper.selectProjectDependencyByTaskId(taskId);
    }
}
