package com.ruoyi.security.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.security.mapper.ScanTaskMapper;
import com.ruoyi.security.domain.ScanTask;
import com.ruoyi.security.service.IScanTaskService;

/**
 * 扫描任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class ScanTaskServiceImpl implements IScanTaskService 
{
    @Autowired
    private ScanTaskMapper scanTaskMapper;

    /**
     * 查询扫描任务
     * 
     * @param taskId 扫描任务主键
     * @return 扫描任务
     */
    @Override
    public ScanTask selectScanTaskByTaskId(Long taskId)
    {
        return scanTaskMapper.selectScanTaskByTaskId(taskId);
    }

    /**
     * 查询扫描任务列表
     * 
     * @param scanTask 扫描任务
     * @return 扫描任务
     */
    @Override
    public List<ScanTask> selectScanTaskList(ScanTask scanTask)
    {
        return scanTaskMapper.selectScanTaskList(scanTask);
    }

    /**
     * 新增扫描任务
     * 
     * @param scanTask 扫描任务
     * @return 结果
     */
    @Override
    public int insertScanTask(ScanTask scanTask)
    {
        scanTask.setCreateTime(new Date());
        scanTask.setCreateBy(SecurityUtils.getUsername());
        return scanTaskMapper.insertScanTask(scanTask);
    }

    /**
     * 修改扫描任务
     * 
     * @param scanTask 扫描任务
     * @return 结果
     */
    @Override
    public int updateScanTask(ScanTask scanTask)
    {
        scanTask.setUpdateTime(new Date());
        scanTask.setUpdateBy(SecurityUtils.getUsername());
        return scanTaskMapper.updateScanTask(scanTask);
    }

    /**
     * 批量删除扫描任务
     * 
     * @param taskIds 需要删除的扫描任务主键
     * @return 结果
     */
    @Override
    public int deleteScanTaskByTaskIds(Long[] taskIds)
    {
        return scanTaskMapper.deleteScanTaskByTaskIds(taskIds);
    }

    /**
     * 删除扫描任务信息
     * 
     * @param taskId 扫描任务主键
     * @return 结果
     */
    @Override
    public int deleteScanTaskByTaskId(Long taskId)
    {
        return scanTaskMapper.deleteScanTaskByTaskId(taskId);
    }
}
