package com.ruoyi.security.service;

import java.util.List;
import com.ruoyi.security.domain.ProjectInfo;

/**
 * 项目信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IProjectInfoService 
{
    /**
     * 查询项目信息
     * 
     * @param projectId 项目信息主键
     * @return 项目信息
     */
    public ProjectInfo selectProjectInfoByProjectId(Long projectId);

    /**
     * 查询项目信息列表
     * 
     * @param projectInfo 项目信息
     * @return 项目信息集合
     */
    public List<ProjectInfo> selectProjectInfoList(ProjectInfo projectInfo);

    /**
     * 新增项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    public int insertProjectInfo(ProjectInfo projectInfo);

    /**
     * 修改项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    public int updateProjectInfo(ProjectInfo projectInfo);

    /**
     * 批量删除项目信息
     * 
     * @param projectIds 需要删除的项目信息主键集合
     * @return 结果
     */
    public int deleteProjectInfoByProjectIds(Long[] projectIds);

    /**
     * 删除项目信息信息
     * 
     * @param projectId 项目信息主键
     * @return 结果
     */
    public int deleteProjectInfoByProjectId(Long projectId);

    /**
     * 校验项目编码是否唯一
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    public boolean checkProjectCodeUnique(ProjectInfo projectInfo);

    /**
     * 测试Git连接
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    public boolean testGitConnection(ProjectInfo projectInfo);
}
