package com.ruoyi.security.service;

import java.util.List;
import com.ruoyi.security.domain.ScanTask;

/**
 * 扫描任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IScanTaskService 
{
    /**
     * 查询扫描任务
     *
     * @param taskId 扫描任务主键
     * @return 扫描任务
     */
    public ScanTask selectScanTaskByTaskId(Long taskId);

    /**
     * 根据项目ID查询扫描任务
     *
     * @param projectId 项目ID
     * @return 扫描任务
     */
    public ScanTask selectScanTaskByProjectId(Long projectId);

    /**
     * 查询扫描任务列表
     * 
     * @param scanTask 扫描任务
     * @return 扫描任务集合
     */
    public List<ScanTask> selectScanTaskList(ScanTask scanTask);

    /**
     * 新增扫描任务
     * 
     * @param scanTask 扫描任务
     * @return 结果
     */
    public int insertScanTask(ScanTask scanTask);

    /**
     * 修改扫描任务
     * 
     * @param scanTask 扫描任务
     * @return 结果
     */
    public int updateScanTask(ScanTask scanTask);

    /**
     * 批量删除扫描任务
     * 
     * @param taskIds 需要删除的扫描任务主键集合
     * @return 结果
     */
    public int deleteScanTaskByTaskIds(Long[] taskIds);

    /**
     * 删除扫描任务信息
     * 
     * @param taskId 扫描任务主键
     * @return 结果
     */
    public int deleteScanTaskByTaskId(Long taskId);
}
