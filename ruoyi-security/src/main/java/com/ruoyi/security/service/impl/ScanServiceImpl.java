package com.ruoyi.security.service.impl;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.domain.ScanTask;
import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.security.service.IScanService;
import com.ruoyi.security.service.IScanTaskService;
import com.ruoyi.security.service.IProjectInfoService;
import com.ruoyi.security.service.IProjectDependencyService;
import com.ruoyi.security.service.IScanReportService;
import com.ruoyi.security.scanner.GitScanner;
import com.ruoyi.security.scanner.MavenScanner;
import com.ruoyi.security.scanner.CveScanner;
import com.ruoyi.security.scanner.LicenseScanner;

/**
 * 扫描服务实现
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class ScanServiceImpl implements IScanService
{
    private static final Logger log = LoggerFactory.getLogger(ScanServiceImpl.class);

    @Autowired
    private IScanTaskService scanTaskService;

    @Autowired
    private IProjectInfoService projectInfoService;

    @Autowired
    private IProjectDependencyService projectDependencyService;

    @Autowired
    private IScanReportService scanReportService;

    @Autowired
    private GitScanner gitScanner;

    @Autowired
    private MavenScanner mavenScanner;

    @Autowired
    private CveScanner cveScanner;

    @Autowired
    private LicenseScanner licenseScanner;

    @Value("${ruoyi.security.scan.workspace:/tmp/security-scan}")
    private String scanWorkspace;

    /**
     * 执行项目扫描
     * 
     * @param projectInfo 项目信息
     * @return 扫描任务
     */
    @Override
    @Transactional
    public ScanTask executeScan(ProjectInfo projectInfo)
    {
        // 创建扫描任务
        ScanTask scanTask = createScanTask(projectInfo);
        
        // 更新项目扫描状态
        projectInfo.setScanStatus("1"); // 扫描中
        projectInfoService.updateProjectInfo(projectInfo);
        
        // 异步执行扫描
        CompletableFuture.runAsync(() -> performScan(scanTask, projectInfo));
        
        return scanTask;
    }

    /**
     * 创建扫描任务
     * 
     * @param projectInfo 项目信息
     * @return 扫描任务
     */
    private ScanTask createScanTask(ProjectInfo projectInfo)
    {
        ScanTask scanTask = new ScanTask();
        scanTask.setProjectId(projectInfo.getProjectId());
        scanTask.setTaskName("扫描任务-" + projectInfo.getProjectName() + "-" + System.currentTimeMillis());
        scanTask.setTaskStatus("1"); // 执行中
        scanTask.setStartTime(new Date());
        scanTask.setCreateBy(SecurityUtils.getUsername());
        
        scanTaskService.insertScanTask(scanTask);
        return scanTask;
    }

    /**
     * 执行扫描流程
     * 
     * @param scanTask 扫描任务
     * @param projectInfo 项目信息
     */
    private void performScan(ScanTask scanTask, ProjectInfo projectInfo)
    {
        String localPath = null;
        
        try
        {
            log.info("开始扫描项目: {}", projectInfo.getProjectName());
            
            // 1. 准备本地工作目录
            localPath = prepareWorkspace(projectInfo);
            
            // 2. 克隆Git仓库
            updateScanProgress(scanTask, "正在克隆Git仓库...");
            if (!gitScanner.cloneOrUpdateRepository(projectInfo, localPath))
            {
                throw new RuntimeException("Git仓库克隆失败");
            }
            
            // 3. 扫描Maven依赖
            updateScanProgress(scanTask, "正在扫描Maven依赖...");
            List<ProjectDependency> dependencies = mavenScanner.scanDependencies(
                localPath, projectInfo.getProjectId(), scanTask.getTaskId());
            
            if (dependencies.isEmpty())
            {
                throw new RuntimeException("未发现Maven依赖，请检查项目是否包含pom.xml文件");
            }
            
            // 4. CVE漏洞扫描
            updateScanProgress(scanTask, "正在扫描CVE漏洞...");
            cveScanner.scanCveVulnerabilities(dependencies);
            
            // 5. 许可证扫描
            updateScanProgress(scanTask, "正在扫描许可证信息...");
            licenseScanner.scanLicenses(dependencies);
            
            // 6. 保存依赖信息
            updateScanProgress(scanTask, "正在保存扫描结果...");
            projectDependencyService.batchInsertProjectDependency(dependencies);
            
            // 7. 生成扫描报告
            updateScanProgress(scanTask, "正在生成扫描报告...");
            scanReportService.generateReport(scanTask.getTaskId(), projectInfo, dependencies);
            
            // 8. 更新任务状态
            completeScanTask(scanTask, dependencies);
            
            // 9. 更新项目状态
            projectInfo.setScanStatus("2"); // 已完成
            projectInfo.setLastScanTime(new Date());
            projectInfoService.updateProjectInfo(projectInfo);
            
            log.info("项目扫描完成: {}, 共发现 {} 个依赖", projectInfo.getProjectName(), dependencies.size());
        }
        catch (Exception e)
        {
            log.error("项目扫描失败: {}", e.getMessage(), e);
            
            // 更新任务失败状态
            scanTask.setTaskStatus("3"); // 执行失败
            scanTask.setEndTime(new Date());
            scanTask.setErrorMsg(e.getMessage());
            scanTaskService.updateScanTask(scanTask);
            
            // 更新项目状态
            projectInfo.setScanStatus("3"); // 扫描失败
            projectInfoService.updateProjectInfo(projectInfo);
        }
        finally
        {
            // 清理本地文件
            if (localPath != null)
            {
                gitScanner.cleanupRepository(localPath);
            }
        }
    }

    /**
     * 准备工作目录
     * 
     * @param projectInfo 项目信息
     * @return 本地路径
     */
    private String prepareWorkspace(ProjectInfo projectInfo)
    {
        String localPath = scanWorkspace + File.separator + 
            projectInfo.getProjectCode() + "_" + System.currentTimeMillis();
        
        File workspaceDir = new File(localPath);
        if (!workspaceDir.exists())
        {
            workspaceDir.mkdirs();
        }
        
        return localPath;
    }

    /**
     * 更新扫描进度
     * 
     * @param scanTask 扫描任务
     * @param progress 进度信息
     */
    private void updateScanProgress(ScanTask scanTask, String progress)
    {
        log.info("扫描进度: {}", progress);
        // 这里可以扩展为实时进度更新
    }

    /**
     * 完成扫描任务
     * 
     * @param scanTask 扫描任务
     * @param dependencies 依赖列表
     */
    private void completeScanTask(ScanTask scanTask, List<ProjectDependency> dependencies)
    {
        scanTask.setTaskStatus("2"); // 已完成
        scanTask.setEndTime(new Date());
        scanTask.setTotalDependencies((long) dependencies.size());
        
        // 统计CVE和许可证风险
        long cveHighCount = 0, cveMediumCount = 0, cveLowCount = 0;
        long licenseHighRiskCount = 0, licenseMediumRiskCount = 0;
        
        for (ProjectDependency dep : dependencies)
        {
            // 统计CVE
            if ("1".equals(dep.getHasCve()) && dep.getMaxCvssScore() != null)
            {
                double cvssScore = dep.getMaxCvssScore().doubleValue();
                if (cvssScore >= 9.0) cveHighCount++;
                else if (cvssScore >= 7.0) cveHighCount++;
                else if (cvssScore >= 4.0) cveMediumCount++;
                else cveLowCount++;
            }
            
            // 统计许可证风险
            if ("3".equals(dep.getLicenseRiskLevel())) licenseHighRiskCount++;
            else if ("2".equals(dep.getLicenseRiskLevel())) licenseMediumRiskCount++;
        }
        
        scanTask.setCveHighCount(cveHighCount);
        scanTask.setCveMediumCount(cveMediumCount);
        scanTask.setCveLowCount(cveLowCount);
        scanTask.setLicenseHighRiskCount(licenseHighRiskCount);
        scanTask.setLicenseMediumRiskCount(licenseMediumRiskCount);
        
        scanTaskService.updateScanTask(scanTask);
    }

    /**
     * 获取扫描状态
     * 
     * @param taskId 任务ID
     * @return 扫描任务
     */
    @Override
    public ScanTask getScanStatus(Long taskId)
    {
        return scanTaskService.selectScanTaskByTaskId(taskId);
    }

    /**
     * 停止扫描任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public boolean stopScan(Long taskId)
    {
        // 简化实现，实际应该支持任务中断
        ScanTask scanTask = scanTaskService.selectScanTaskByTaskId(taskId);
        if (scanTask != null && "1".equals(scanTask.getTaskStatus()))
        {
            scanTask.setTaskStatus("3"); // 设为失败
            scanTask.setEndTime(new Date());
            scanTask.setErrorMsg("用户手动停止");
            scanTaskService.updateScanTask(scanTask);
            return true;
        }
        return false;
    }
}
