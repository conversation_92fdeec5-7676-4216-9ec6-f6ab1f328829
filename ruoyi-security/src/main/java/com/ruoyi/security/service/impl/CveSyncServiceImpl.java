package com.ruoyi.security.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.security.config.CveApiProperties;
import com.ruoyi.security.domain.CveInfo;
import com.ruoyi.security.mapper.CveInfoMapper;
import com.ruoyi.security.service.ICveSyncService;
import com.ruoyi.security.service.ICveInfoService;

/**
 * CVE数据同步服务实现
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class CveSyncServiceImpl implements ICveSyncService {

    private static final Logger logger = LoggerFactory.getLogger(CveSyncServiceImpl.class);

    @Autowired
    private CveApiProperties cveApiProperties;

    @Autowired
    private ICveInfoService cveInfoService;

    @Autowired
    private CveInfoMapper cveInfoMapper;

    private final RestTemplate restTemplate;

    public CveSyncServiceImpl() {
        // 配置RestTemplate超时时间
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(30000); // 30秒连接超时
        factory.setReadTimeout(60000);    // 60秒读取超时
        this.restTemplate = new RestTemplate(factory);
    }

    // 同步状态控制
    private final AtomicBoolean isSyncing = new AtomicBoolean(false);
    private final AtomicInteger syncProgress = new AtomicInteger(0);
    private volatile String syncMessage = "";

    @Override
    public AjaxResult syncCveData(String startDate, String endDate) {
        if (!cveApiProperties.getNvd().isApiKeyConfigured()) {
            return AjaxResult.error("CVE API Key未配置，请在配置文件中设置 ruoyi.security.cve.nvd.apiKey");
        }

        if (isSyncing.get()) {
            return AjaxResult.error("CVE数据同步正在进行中，请稍后再试");
        }

        // 异步执行同步任务
        new Thread(() -> {
            try {
                isSyncing.set(true);
                syncProgress.set(0);
                syncMessage = "开始同步CVE数据...";
                
                performSync(startDate, endDate);
                
                syncMessage = "CVE数据同步完成";
            } catch (Exception e) {
                logger.error("CVE数据同步失败", e);
                syncMessage = "CVE数据同步失败: " + e.getMessage();
            } finally {
                isSyncing.set(false);
            }
        }).start();

        return AjaxResult.success("CVE数据同步已启动，请稍后查看同步状态");
    }

    @Override
    public AjaxResult syncRecentCveData() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(30);
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return syncCveData(startDate.format(formatter), endDate.format(formatter));
    }

    @Override
    public AjaxResult checkApiConfig() {
        CveApiProperties.NvdConfig nvdConfig = cveApiProperties.getNvd();

        // 首先测试公共访问（无API密钥）
        try {
            String publicUrl = nvdConfig.getBaseUrl() + "?resultsPerPage=1";
            logger.info("测试NVD API公共访问，URL: {}", publicUrl);

            HttpHeaders headers = new HttpHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> publicResponse = restTemplate.exchange(publicUrl, HttpMethod.GET, entity, String.class);

            if (publicResponse.getStatusCode().is2xxSuccessful()) {
                logger.info("NVD API公共访问测试成功");

                // 如果配置了API密钥，测试API密钥访问
                if (nvdConfig.isApiKeyConfigured()) {
                    try {
                        String apiUrl = nvdConfig.getBaseUrl() + "?resultsPerPage=1&apiKey=" + nvdConfig.getApiKey();
                        logger.info("测试NVD API密钥访问");

                        ResponseEntity<String> apiResponse = restTemplate.exchange(apiUrl, HttpMethod.GET, entity, String.class);
                        if (apiResponse.getStatusCode().is2xxSuccessful()) {
                            logger.info("NVD API密钥访问测试成功");
                            return AjaxResult.success("API配置正常（使用API密钥）")
                                    .put("configured", true)
                                    .put("hasApiKey", true)
                                    .put("baseUrl", nvdConfig.getBaseUrl());
                        } else {
                            logger.warn("API密钥访问失败，状态码: {}，将使用公共访问", apiResponse.getStatusCode());
                            return AjaxResult.success("API配置正常（使用公共访问，API密钥无效）")
                                    .put("configured", true)
                                    .put("hasApiKey", false)
                                    .put("baseUrl", nvdConfig.getBaseUrl())
                                    .put("warning", "API密钥无效，使用公共访问");
                        }
                    } catch (Exception apiException) {
                        logger.warn("API密钥访问失败: {}，将使用公共访问", apiException.getMessage());
                        return AjaxResult.success("API配置正常（使用公共访问，API密钥访问失败）")
                                .put("configured", true)
                                .put("hasApiKey", false)
                                .put("baseUrl", nvdConfig.getBaseUrl())
                                .put("warning", "API密钥访问失败，使用公共访问");
                    }
                } else {
                    logger.info("未配置API密钥，使用公共访问");
                    return AjaxResult.success("API配置正常（使用公共访问）")
                            .put("configured", true)
                            .put("hasApiKey", false)
                            .put("baseUrl", nvdConfig.getBaseUrl())
                            .put("info", "未配置API密钥，使用公共访问");
                }
            } else {
                logger.error("NVD API公共访问测试失败，状态码: {}", publicResponse.getStatusCode());
                return AjaxResult.error("API连接测试失败")
                        .put("configured", false)
                        .put("statusCode", publicResponse.getStatusCode().value());
            }
        } catch (Exception e) {
            logger.error("API连接测试失败", e);
            // 提供更详细的错误信息
            String errorMsg = e.getMessage();
            if (e instanceof org.springframework.web.client.HttpClientErrorException) {
                org.springframework.web.client.HttpClientErrorException httpError =
                    (org.springframework.web.client.HttpClientErrorException) e;
                errorMsg = "HTTP " + httpError.getStatusCode() + ": " + httpError.getStatusText();
            }
            return AjaxResult.error("API连接测试失败: " + errorMsg)
                    .put("configured", false);
        }
    }

    @Override
    public AjaxResult getSyncStatus() {
        return AjaxResult.success()
                .put("syncing", isSyncing.get())
                .put("progress", syncProgress.get())
                .put("message", syncMessage);
    }

    @Override
    public AjaxResult stopSync() {
        if (!isSyncing.get()) {
            return AjaxResult.error("当前没有正在进行的同步任务");
        }

        // 这里可以添加停止同步的逻辑
        // 由于是简单实现，暂时只是标记停止
        syncMessage = "同步已被用户停止";
        return AjaxResult.success("同步停止指令已发送");
    }

    /**
     * 执行实际的同步操作
     */
    private void performSync(String startDate, String endDate) {
        CveApiProperties.NvdConfig nvdConfig = cveApiProperties.getNvd();

        try {
            syncMessage = "正在从NVD获取CVE数据...";

            // 构建API请求URL - NVD API 2.0 格式
            StringBuilder urlBuilder = new StringBuilder(nvdConfig.getBaseUrl());
            urlBuilder.append("?pubStartDate=").append(startDate).append("T00:00:00.000");
            urlBuilder.append("&pubEndDate=").append(endDate).append("T23:59:59.999");
            urlBuilder.append("&resultsPerPage=").append(nvdConfig.getResultsPerPage());

            HttpHeaders headers = new HttpHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = null;

            // 如果配置了API密钥，先尝试使用API密钥访问
            if (nvdConfig.getApiKey() != null && !nvdConfig.getApiKey().trim().isEmpty()) {
                try {
                    String apiUrl = urlBuilder.toString() + "&apiKey=" + nvdConfig.getApiKey();
                    logger.info("尝试使用API密钥获取CVE数据");
                    response = restTemplate.exchange(apiUrl, HttpMethod.GET, entity, String.class);

                    if (response.getStatusCode().is2xxSuccessful()) {
                        logger.info("使用API密钥成功获取CVE数据");
                    }
                } catch (Exception apiException) {
                    logger.warn("API密钥访问失败: {}，尝试使用公共访问", apiException.getMessage());
                    response = null; // 重置响应，准备使用公共访问
                }
            }

            // 如果API密钥访问失败或未配置，使用公共访问
            if (response == null || !response.getStatusCode().is2xxSuccessful()) {
                try {
                    String publicUrl = urlBuilder.toString();
                    logger.info("使用公共访问获取CVE数据");
                    response = restTemplate.exchange(publicUrl, HttpMethod.GET, entity, String.class);

                    if (response.getStatusCode().is2xxSuccessful()) {
                        logger.info("使用公共访问成功获取CVE数据");
                    }
                } catch (Exception publicException) {
                    logger.error("公共访问也失败: {}", publicException.getMessage());
                    throw new RuntimeException("无法访问NVD API，API密钥和公共访问都失败");
                }
            }

            if (response != null && response.getStatusCode().is2xxSuccessful()) {
                syncMessage = "CVE数据获取成功，正在处理...";

                // 解析并保存CVE数据
                int savedCount = parseCveDataAndSave(response.getBody());

                syncProgress.set(100);
                syncMessage = "CVE数据同步完成，共保存 " + savedCount + " 条记录";
                logger.info("CVE数据同步完成，日期范围: {} 到 {}，保存记录数: {}", startDate, endDate, savedCount);
            } else {
                throw new RuntimeException("API请求失败，状态码: " + (response != null ? response.getStatusCode() : "null"));
            }

        } catch (Exception e) {
            logger.error("CVE数据同步失败", e);
            throw new RuntimeException("CVE数据同步失败: " + e.getMessage());
        }
    }

    /**
     * 解析CVE数据并保存到数据库
     */
    private int parseCveDataAndSave(String jsonData) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(jsonData);

            JsonNode vulnerabilities = rootNode.get("vulnerabilities");
            if (vulnerabilities == null || !vulnerabilities.isArray()) {
                logger.warn("响应中没有找到vulnerabilities数组");
                return 0;
            }

            int savedCount = 0;
            for (JsonNode vulnNode : vulnerabilities) {
                JsonNode cveNode = vulnNode.get("cve");
                if (cveNode == null) {
                    continue;
                }

                try {
                    CveInfo cveInfo = parseCveNode(cveNode);
                    if (cveInfo != null) {
                        // 检查是否已存在
                        CveInfo existing = cveInfoMapper.selectCveInfoByCveId(cveInfo.getCveId());
                        if (existing == null) {
                            cveInfoMapper.insertCveInfo(cveInfo);
                            savedCount++;
                            logger.debug("保存新CVE记录: {}", cveInfo.getCveId());
                        } else {
                            // 更新现有记录，保持原有的创建信息
                            cveInfo.setCreateBy(existing.getCreateBy());
                            cveInfo.setCreateTime(existing.getCreateTime());
                            cveInfoMapper.updateCveInfo(cveInfo);
                            logger.debug("更新CVE记录: {}", cveInfo.getCveId());
                        }
                    }
                } catch (Exception e) {
                    logger.error("解析CVE记录失败", e);
                }
            }

            logger.info("成功处理CVE数据，保存/更新记录数: {}", savedCount);
            return savedCount;

        } catch (Exception e) {
            logger.error("解析CVE数据失败", e);
            throw new RuntimeException("解析CVE数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析单个CVE节点
     */
    private CveInfo parseCveNode(JsonNode cveNode) {
        try {
            CveInfo cveInfo = new CveInfo();

            // CVE ID
            JsonNode idNode = cveNode.get("id");
            if (idNode != null) {
                cveInfo.setCveId(idNode.asText());
            }

            // 发布时间
            JsonNode publishedNode = cveNode.get("published");
            if (publishedNode != null) {
                String publishedStr = publishedNode.asText();
                // 解析ISO 8601格式的时间
                try {
                    LocalDateTime publishedTime = LocalDateTime.parse(publishedStr.substring(0, 19));
                    cveInfo.setPublishedDate(java.sql.Timestamp.valueOf(publishedTime));
                } catch (Exception e) {
                    logger.warn("解析发布时间失败: {}", publishedStr);
                }
            }

            // 最后修改时间
            JsonNode lastModifiedNode = cveNode.get("lastModified");
            if (lastModifiedNode != null) {
                String lastModifiedStr = lastModifiedNode.asText();
                try {
                    LocalDateTime lastModifiedTime = LocalDateTime.parse(lastModifiedStr.substring(0, 19));
                    cveInfo.setLastModifiedDate(java.sql.Timestamp.valueOf(lastModifiedTime));
                } catch (Exception e) {
                    logger.warn("解析最后修改时间失败: {}", lastModifiedStr);
                }
            }

            // 描述
            JsonNode descriptionsNode = cveNode.get("descriptions");
            if (descriptionsNode != null && descriptionsNode.isArray()) {
                for (JsonNode descNode : descriptionsNode) {
                    JsonNode langNode = descNode.get("lang");
                    JsonNode valueNode = descNode.get("value");
                    if (langNode != null && "en".equals(langNode.asText()) && valueNode != null) {
                        cveInfo.setDescription(valueNode.asText());
                        break;
                    }
                }
            }

            // CVSS分数 - 尝试获取最新版本的CVSS
            parseCvssScore(cveNode, cveInfo);

            // 严重程度
            cveInfo.setSeverity(extractSeverity(cveNode));

            // 引用链接
            JsonNode referencesNode = cveNode.get("references");
            if (referencesNode != null && referencesNode.isArray()) {
                StringBuilder references = new StringBuilder();
                for (JsonNode refNode : referencesNode) {
                    JsonNode urlNode = refNode.get("url");
                    if (urlNode != null) {
                        if (references.length() > 0) {
                            references.append("\n");
                        }
                        references.append(urlNode.asText());
                    }
                }
                cveInfo.setReferences(references.toString());
            }

            // 设置创建信息
            cveInfo.setCreateBy("system");
            cveInfo.setCreateTime(new Date());
            cveInfo.setUpdateBy("system");
            cveInfo.setUpdateTime(new Date());

            return cveInfo;

        } catch (Exception e) {
            logger.error("解析CVE节点失败", e);
            return null;
        }
    }

    /**
     * 解析CVSS分数
     */
    private void parseCvssScore(JsonNode cveNode, CveInfo cveInfo) {
        JsonNode metricsNode = cveNode.get("metrics");
        if (metricsNode == null) {
            return;
        }

        // 优先使用CVSS v3.1，然后v3.0，最后v2.0
        String[] cvssVersions = {"cvssMetricV31", "cvssMetricV30", "cvssMetricV2"};

        for (String version : cvssVersions) {
            JsonNode cvssMetrics = metricsNode.get(version);
            if (cvssMetrics != null && cvssMetrics.isArray() && cvssMetrics.size() > 0) {
                JsonNode firstMetric = cvssMetrics.get(0);
                JsonNode cvssData = firstMetric.get("cvssData");
                if (cvssData != null) {
                    JsonNode baseScoreNode = cvssData.get("baseScore");
                    if (baseScoreNode != null) {
                        cveInfo.setCvssScore(baseScoreNode.asDouble());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 提取严重程度
     */
    private String extractSeverity(JsonNode cveNode) {
        JsonNode metricsNode = cveNode.get("metrics");
        if (metricsNode == null) {
            return "UNKNOWN";
        }

        // 优先使用CVSS v3.1，然后v3.0，最后v2.0
        String[] cvssVersions = {"cvssMetricV31", "cvssMetricV30", "cvssMetricV2"};

        for (String version : cvssVersions) {
            JsonNode cvssMetrics = metricsNode.get(version);
            if (cvssMetrics != null && cvssMetrics.isArray() && cvssMetrics.size() > 0) {
                JsonNode firstMetric = cvssMetrics.get(0);
                JsonNode baseSeverityNode = firstMetric.get("baseSeverity");
                if (baseSeverityNode != null) {
                    return baseSeverityNode.asText();
                }
            }
        }

        return "UNKNOWN";
    }
}
