package com.ruoyi.security.service;

import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.domain.ScanTask;

/**
 * 扫描服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IScanService 
{
    /**
     * 执行项目扫描
     * 
     * @param projectInfo 项目信息
     * @return 扫描任务
     */
    public ScanTask executeScan(ProjectInfo projectInfo);

    /**
     * 获取扫描状态
     * 
     * @param taskId 任务ID
     * @return 扫描任务
     */
    public ScanTask getScanStatus(Long taskId);

    /**
     * 停止扫描任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    public boolean stopScan(Long taskId);
}
