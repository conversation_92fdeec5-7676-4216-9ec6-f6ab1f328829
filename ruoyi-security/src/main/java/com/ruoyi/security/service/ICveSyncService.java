package com.ruoyi.security.service;

import com.ruoyi.common.core.domain.AjaxResult;

/**
 * CVE数据同步服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ICveSyncService {

    /**
     * 同步CVE数据
     * 
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 同步结果
     */
    AjaxResult syncCveData(String startDate, String endDate);

    /**
     * 同步最近的CVE数据（最近30天）
     * 
     * @return 同步结果
     */
    AjaxResult syncRecentCveData();

    /**
     * 检查API配置状态
     * 
     * @return 配置状态
     */
    AjaxResult checkApiConfig();

    /**
     * 获取同步状态
     * 
     * @return 同步状态
     */
    AjaxResult getSyncStatus();

    /**
     * 停止正在进行的同步
     * 
     * @return 操作结果
     */
    AjaxResult stopSync();
}
