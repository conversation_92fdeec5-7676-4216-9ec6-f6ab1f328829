package com.ruoyi.security.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.security.mapper.LicenseInfoMapper;
import com.ruoyi.security.domain.LicenseInfo;
import com.ruoyi.security.service.ILicenseInfoService;

/**
 * 许可证信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class LicenseInfoServiceImpl implements ILicenseInfoService 
{
    @Autowired
    private LicenseInfoMapper licenseInfoMapper;

    /**
     * 查询许可证信息
     * 
     * @param licenseId 许可证信息主键
     * @return 许可证信息
     */
    @Override
    public LicenseInfo selectLicenseInfoByLicenseId(Long licenseId)
    {
        return licenseInfoMapper.selectLicenseInfoByLicenseId(licenseId);
    }

    /**
     * 查询许可证信息列表
     * 
     * @param licenseInfo 许可证信息
     * @return 许可证信息
     */
    @Override
    public List<LicenseInfo> selectLicenseInfoList(LicenseInfo licenseInfo)
    {
        return licenseInfoMapper.selectLicenseInfoList(licenseInfo);
    }

    /**
     * 新增许可证信息
     * 
     * @param licenseInfo 许可证信息
     * @return 结果
     */
    @Override
    public int insertLicenseInfo(LicenseInfo licenseInfo)
    {
        licenseInfo.setCreateTime(DateUtils.getNowDate());
        return licenseInfoMapper.insertLicenseInfo(licenseInfo);
    }

    /**
     * 修改许可证信息
     * 
     * @param licenseInfo 许可证信息
     * @return 结果
     */
    @Override
    public int updateLicenseInfo(LicenseInfo licenseInfo)
    {
        licenseInfo.setUpdateTime(DateUtils.getNowDate());
        return licenseInfoMapper.updateLicenseInfo(licenseInfo);
    }

    /**
     * 批量删除许可证信息
     * 
     * @param licenseIds 需要删除的许可证信息主键
     * @return 结果
     */
    @Override
    public int deleteLicenseInfoByLicenseIds(Long[] licenseIds)
    {
        return licenseInfoMapper.deleteLicenseInfoByLicenseIds(licenseIds);
    }

    /**
     * 删除许可证信息信息
     * 
     * @param licenseId 许可证信息主键
     * @return 结果
     */
    @Override
    public int deleteLicenseInfoByLicenseId(Long licenseId)
    {
        return licenseInfoMapper.deleteLicenseInfoByLicenseId(licenseId);
    }

    /**
     * 根据许可证类型查询许可证信息
     * 
     * @param licenseType 许可证类型
     * @return 许可证信息
     */
    @Override
    public LicenseInfo selectLicenseInfoByType(String licenseType)
    {
        return licenseInfoMapper.selectLicenseInfoByType(licenseType);
    }

    /**
     * 校验许可证名称是否唯一
     * 
     * @param licenseInfo 许可证信息
     * @return 结果
     */
    @Override
    public boolean checkLicenseNameUnique(LicenseInfo licenseInfo)
    {
        Long licenseId = StringUtils.isNull(licenseInfo.getLicenseId()) ? -1L : licenseInfo.getLicenseId();
        LicenseInfo info = licenseInfoMapper.checkLicenseNameUnique(licenseInfo.getLicenseName());
        if (StringUtils.isNotNull(info) && info.getLicenseId().longValue() != licenseId.longValue())
        {
            return false;
        }
        return true;
    }
}
