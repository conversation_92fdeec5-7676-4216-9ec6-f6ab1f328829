package com.ruoyi.security.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * CVE API配置属性
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
@ConfigurationProperties(prefix = "ruoyi.security.cve")
public class CveApiProperties {

    /**
     * NVD API配置
     */
    private NvdConfig nvd = new NvdConfig();

    public NvdConfig getNvd() {
        return nvd;
    }

    public void setNvd(NvdConfig nvd) {
        this.nvd = nvd;
    }

    /**
     * NVD API配置类
     */
    public static class NvdConfig {
        /**
         * API Key
         */
        private String apiKey;

        /**
         * API基础URL
         */
        private String baseUrl = "https://services.nvd.nist.gov/rest/json/cves/2.0";

        /**
         * 请求超时时间（秒）
         */
        private int timeout = 30;

        /**
         * 请求间隔时间（毫秒）
         */
        private long requestInterval = 6000;

        /**
         * 每次请求的最大结果数
         */
        private int resultsPerPage = 2000;

        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public long getRequestInterval() {
            return requestInterval;
        }

        public void setRequestInterval(long requestInterval) {
            this.requestInterval = requestInterval;
        }

        public int getResultsPerPage() {
            return resultsPerPage;
        }

        public void setResultsPerPage(int resultsPerPage) {
            this.resultsPerPage = resultsPerPage;
        }

        /**
         * 检查API Key是否已配置
         */
        public boolean isApiKeyConfigured() {
            return apiKey != null && !apiKey.trim().isEmpty();
        }
    }
}
