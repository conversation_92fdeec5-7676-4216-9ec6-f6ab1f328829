package com.ruoyi.security.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.security.config.CveApiProperties;
import com.ruoyi.security.domain.CveInfo;
import com.ruoyi.security.mapper.CveInfoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * CVE批量导入工具
 */
@Component
public class CveBulkImporter {

    private static final Logger logger = LoggerFactory.getLogger(CveBulkImporter.class);

    @Autowired
    private CveApiProperties cveApiProperties;

    @Autowired
    private CveInfoMapper cveInfoMapper;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 导入指定年份的CVE数据
     */
    public void importCveDataByYear(int year) {
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);
        
        logger.info("开始导入{}年的CVE数据: {} 到 {}", year, startDate, endDate);
        importCveDataByDateRange(startDate, endDate);
    }

    /**
     * 导入最近指定天数的CVE数据
     */
    public void importRecentCveData(int days) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days);
        
        logger.info("开始导入最近{}天的CVE数据: {} 到 {}", days, startDate, endDate);
        importCveDataByDateRange(startDate, endDate);
    }

    /**
     * 导入指定日期范围的CVE数据
     */
    public void importCveDataByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            int startIndex = 0;
            int resultsPerPage = 50; // 每页50条记录，避免请求过大
            int totalImported = 0;
            int totalUpdated = 0;
            boolean hasMoreData = true;
            int pageNumber = 1;

            logger.info("开始批量导入CVE数据，日期范围: {} 到 {}", startDate, endDate);

            while (hasMoreData) {
                logger.info("正在处理第 {} 页数据...", pageNumber);
                
                String url = buildApiUrl(startDate, endDate, startIndex, resultsPerPage);
                logger.debug("API请求URL: {}", url);

                try {
                    ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
                    
                    if (response.getStatusCode() != HttpStatus.OK) {
                        logger.error("API请求失败，状态码: {}", response.getStatusCode());
                        break;
                    }

                    JsonNode rootNode = objectMapper.readTree(response.getBody());
                    JsonNode vulnerabilitiesNode = rootNode.get("vulnerabilities");
                    
                    if (vulnerabilitiesNode == null || !vulnerabilitiesNode.isArray()) {
                        logger.warn("响应中没有找到vulnerabilities数组");
                        break;
                    }

                    int currentPageCount = vulnerabilitiesNode.size();
                    if (currentPageCount == 0) {
                        logger.info("当前页没有数据，导入完成");
                        hasMoreData = false;
                        break;
                    }

                    // 解析并保存CVE数据
                    List<CveInfo> cveList = new ArrayList<>();
                    for (JsonNode vulnNode : vulnerabilitiesNode) {
                        JsonNode cveNode = vulnNode.get("cve");
                        if (cveNode != null) {
                            try {
                                CveInfo cveInfo = parseCveNode(cveNode);
                                if (cveInfo != null) {
                                    cveList.add(cveInfo);
                                }
                            } catch (Exception e) {
                                logger.warn("解析CVE记录失败: {}", e.getMessage());
                            }
                        }
                    }

                    // 批量保存到数据库
                    ImportResult result = saveCveList(cveList);
                    totalImported += result.insertedCount;
                    totalUpdated += result.updatedCount;
                    
                    logger.info("第 {} 页处理完成: 获取 {} 条，新增 {} 条，更新 {} 条", 
                               pageNumber, currentPageCount, result.insertedCount, result.updatedCount);

                    // 检查是否还有更多数据
                    JsonNode totalResultsNode = rootNode.get("totalResults");
                    if (totalResultsNode != null) {
                        int totalResults = totalResultsNode.asInt();
                        logger.info("总记录数: {}, 已处理: {}", totalResults, startIndex + currentPageCount);
                        
                        if (startIndex + currentPageCount >= totalResults) {
                            hasMoreData = false;
                            logger.info("已获取所有数据");
                        }
                    }

                    // 如果当前页数据少于每页限制，说明已经是最后一页
                    if (currentPageCount < resultsPerPage) {
                        hasMoreData = false;
                        logger.info("已到达最后一页");
                    }

                    startIndex += resultsPerPage;
                    pageNumber++;

                    // 添加延迟以避免API限制
                    Thread.sleep(3000); // 3秒延迟

                } catch (Exception e) {
                    logger.error("处理第 {} 页时发生错误: {}", pageNumber, e.getMessage());
                    // 继续处理下一页
                    startIndex += resultsPerPage;
                    pageNumber++;
                    Thread.sleep(5000); // 错误后等待更长时间
                }
            }

            logger.info("CVE数据导入完成！统计信息:");
            logger.info("- 新增记录: {} 条", totalImported);
            logger.info("- 更新记录: {} 条", totalUpdated);
            logger.info("- 总处理记录: {} 条", totalImported + totalUpdated);

        } catch (Exception e) {
            logger.error("批量导入CVE数据时发生严重错误", e);
        }
    }

    /**
     * 构建API请求URL
     */
    private String buildApiUrl(LocalDate startDate, LocalDate endDate, int startIndex, int resultsPerPage) {
        StringBuilder url = new StringBuilder(cveApiProperties.getBaseUrl());
        url.append("?resultsPerPage=").append(resultsPerPage);
        url.append("&startIndex=").append(startIndex);
        
        // 添加日期过滤 - 使用发布日期过滤
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        String pubStartDate = startDate.atStartOfDay().format(formatter);
        String pubEndDate = endDate.atTime(23, 59, 59).format(formatter);
        
        url.append("&pubStartDate=").append(pubStartDate);
        url.append("&pubEndDate=").append(pubEndDate);

        // 如果有API密钥，添加到URL中（但根据之前的测试，公共访问更稳定）
        // if (cveApiProperties.getApiKey() != null && !cveApiProperties.getApiKey().isEmpty()) {
        //     url.append("&apiKey=").append(cveApiProperties.getApiKey());
        // }

        return url.toString();
    }

    /**
     * 解析CVE节点数据
     */
    private CveInfo parseCveNode(JsonNode cveNode) {
        try {
            CveInfo cveInfo = new CveInfo();

            // CVE ID
            JsonNode idNode = cveNode.get("id");
            if (idNode != null) {
                cveInfo.setCveId(idNode.asText());
            } else {
                return null;
            }

            // 描述
            JsonNode descriptionsNode = cveNode.get("descriptions");
            if (descriptionsNode != null && descriptionsNode.isArray()) {
                for (JsonNode descNode : descriptionsNode) {
                    JsonNode langNode = descNode.get("lang");
                    if (langNode != null && "en".equals(langNode.asText())) {
                        JsonNode valueNode = descNode.get("value");
                        if (valueNode != null) {
                            cveInfo.setDescription(valueNode.asText());
                            break;
                        }
                    }
                }
            }

            // 发布时间
            JsonNode publishedNode = cveNode.get("published");
            if (publishedNode != null) {
                String publishedStr = publishedNode.asText();
                try {
                    LocalDateTime publishedTime = LocalDateTime.parse(publishedStr.substring(0, 19));
                    cveInfo.setPublishedDate(java.sql.Timestamp.valueOf(publishedTime));
                } catch (Exception e) {
                    logger.debug("解析发布时间失败: {}", publishedStr);
                }
            }

            // 最后修改时间
            JsonNode lastModifiedNode = cveNode.get("lastModified");
            if (lastModifiedNode != null) {
                String lastModifiedStr = lastModifiedNode.asText();
                try {
                    LocalDateTime lastModifiedTime = LocalDateTime.parse(lastModifiedStr.substring(0, 19));
                    cveInfo.setUpdateDate(java.sql.Timestamp.valueOf(lastModifiedTime));
                } catch (Exception e) {
                    logger.debug("解析最后修改时间失败: {}", lastModifiedStr);
                }
            }

            // CVSS评分和严重程度
            extractCvssAndSeverity(cveNode, cveInfo);

            // 参考链接
            JsonNode referencesNode = cveNode.get("references");
            if (referencesNode != null && referencesNode.isArray()) {
                StringBuilder references = new StringBuilder();
                for (JsonNode refNode : referencesNode) {
                    JsonNode urlNode = refNode.get("url");
                    if (urlNode != null) {
                        if (references.length() > 0) {
                            references.append("\n");
                        }
                        references.append(urlNode.asText());
                    }
                }
                cveInfo.setReferences(references.toString());
            }

            // 设置默认状态
            cveInfo.setStatus("0");

            return cveInfo;

        } catch (Exception e) {
            logger.error("解析CVE节点时发生错误", e);
            return null;
        }
    }

    /**
     * 提取CVSS评分和严重程度
     */
    private void extractCvssAndSeverity(JsonNode cveNode, CveInfo cveInfo) {
        JsonNode metricsNode = cveNode.get("metrics");
        if (metricsNode == null) {
            cveInfo.setSeverityLevel("1");
            return;
        }

        String[] cvssVersions = {"cvssMetricV31", "cvssMetricV30", "cvssMetricV2"};

        for (String version : cvssVersions) {
            JsonNode cvssMetrics = metricsNode.get(version);
            if (cvssMetrics != null && cvssMetrics.isArray() && cvssMetrics.size() > 0) {
                JsonNode firstMetric = cvssMetrics.get(0);
                JsonNode cvssData = firstMetric.get("cvssData");
                if (cvssData != null) {
                    JsonNode baseScoreNode = cvssData.get("baseScore");
                    if (baseScoreNode != null) {
                        cveInfo.setCvssScore(new BigDecimal(baseScoreNode.asDouble()));
                    }

                    JsonNode baseSeverityNode = firstMetric.get("baseSeverity");
                    if (baseSeverityNode != null) {
                        String severity = baseSeverityNode.asText().toUpperCase();
                        cveInfo.setSeverityLevel(convertSeverityToCode(severity));
                    }
                    return;
                }
            }
        }

        cveInfo.setSeverityLevel("1");
    }

    /**
     * 转换严重程度代码
     */
    private String convertSeverityToCode(String severity) {
        switch (severity) {
            case "LOW": return "1";
            case "MEDIUM": return "2";
            case "HIGH": return "3";
            case "CRITICAL": return "4";
            default: return "1";
        }
    }

    /**
     * 批量保存CVE列表
     */
    private ImportResult saveCveList(List<CveInfo> cveList) {
        ImportResult result = new ImportResult();
        
        for (CveInfo cveInfo : cveList) {
            try {
                CveInfo existing = cveInfoMapper.selectCveInfoByCveId(cveInfo.getCveId());
                if (existing == null) {
                    cveInfoMapper.insertCveInfo(cveInfo);
                    result.insertedCount++;
                } else {
                    cveInfo.setCreateBy(existing.getCreateBy());
                    cveInfo.setCreateTime(existing.getCreateTime());
                    cveInfoMapper.updateCveInfo(cveInfo);
                    result.updatedCount++;
                }
            } catch (Exception e) {
                logger.error("保存CVE记录失败: {}, 错误: {}", cveInfo.getCveId(), e.getMessage());
                result.errorCount++;
            }
        }
        
        return result;
    }

    /**
     * 导入结果统计
     */
    private static class ImportResult {
        int insertedCount = 0;
        int updatedCount = 0;
        int errorCount = 0;
    }
}
