package com.ruoyi.security;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * CVE API测试
 */
public class CveApiTest {

    public static void main(String[] args) {
        testNvdApiConnection();
    }

    public static void testNvdApiConnection() {
        try {
            String apiKey = "4be784e5-5c6c-4c7a-bf20-40b9f852e56d";
            String baseUrl = "https://services.nvd.nist.gov/rest/json/cves/2.0";

            // 首先测试不使用API密钥的公共访问
            System.out.println("=== 测试公共访问（无API密钥）===");
            String publicUrl = baseUrl + "?resultsPerPage=1";
            System.out.println("测试URL: " + publicUrl);
            testApiCall(publicUrl);

            // 然后测试使用API密钥的访问
            System.out.println("\n=== 测试API密钥访问 ===");
            String testUrl = baseUrl + "?resultsPerPage=1&apiKey=" + apiKey;
            System.out.println("测试URL: " + testUrl.replaceAll("apiKey=[^&]*", "apiKey=***"));
            testApiCall(testUrl);

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testApiCall(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(60000);
            connection.setRequestProperty("Accept", "application/json");

            int responseCode = connection.getResponseCode();
            System.out.println("响应状态码: " + responseCode);

            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                if (connection.getErrorStream() != null) {
                    reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                } else {
                    System.out.println("错误流为空，响应状态码: " + responseCode);
                    return;
                }
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            System.out.println("响应内容长度: " + response.length());
            if (response.length() < 1000) {
                System.out.println("响应内容: " + response.toString());
            } else {
                System.out.println("响应内容: " + response.substring(0, 500) + "...");
            }

        } catch (Exception e) {
            System.err.println("API调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
