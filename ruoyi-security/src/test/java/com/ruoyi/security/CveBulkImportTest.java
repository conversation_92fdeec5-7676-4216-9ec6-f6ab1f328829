package com.ruoyi.security;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.security.config.CveApiProperties;
import com.ruoyi.security.domain.CveInfo;
import com.ruoyi.security.mapper.CveInfoMapper;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * CVE批量导入测试程序
 * 用于导入历史CVE数据到数据库
 */
@SpringBootTest
public class CveBulkImportTest {

    private static final Logger logger = LoggerFactory.getLogger(CveBulkImportTest.class);

    @Autowired
    private CveApiProperties cveApiProperties;

    @Autowired
    private CveInfoMapper cveInfoMapper;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 导入最近30天的CVE数据
     */
    @Test
    public void importRecentCveData() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(30);
        
        logger.info("开始导入最近30天的CVE数据: {} 到 {}", startDate, endDate);
        importCveDataByDateRange(startDate, endDate);
    }

    /**
     * 导入最近一年的CVE数据
     */
    @Test
    public void importLastYearCveData() {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusYears(1);
        
        logger.info("开始导入最近一年的CVE数据: {} 到 {}", startDate, endDate);
        importCveDataByDateRange(startDate, endDate);
    }

    /**
     * 导入2024年的CVE数据
     */
    @Test
    public void import2024CveData() {
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 12, 31);
        
        logger.info("开始导入2024年的CVE数据: {} 到 {}", startDate, endDate);
        importCveDataByDateRange(startDate, endDate);
    }

    /**
     * 导入2023年的CVE数据
     */
    @Test
    public void import2023CveData() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 12, 31);
        
        logger.info("开始导入2023年的CVE数据: {} 到 {}", startDate, endDate);
        importCveDataByDateRange(startDate, endDate);
    }

    /**
     * 按日期范围导入CVE数据
     */
    private void importCveDataByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            int startIndex = 0;
            int resultsPerPage = 100; // 每页100条记录
            int totalImported = 0;
            boolean hasMoreData = true;

            while (hasMoreData) {
                logger.info("正在获取第 {} 页数据，每页 {} 条记录", (startIndex / resultsPerPage) + 1, resultsPerPage);
                
                String url = buildApiUrl(startDate, endDate, startIndex, resultsPerPage);
                logger.debug("API请求URL: {}", url);

                ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
                
                if (response.getStatusCode() != HttpStatus.OK) {
                    logger.error("API请求失败，状态码: {}", response.getStatusCode());
                    break;
                }

                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode vulnerabilitiesNode = rootNode.get("vulnerabilities");
                
                if (vulnerabilitiesNode == null || !vulnerabilitiesNode.isArray()) {
                    logger.warn("响应中没有找到vulnerabilities数组");
                    break;
                }

                int currentPageCount = vulnerabilitiesNode.size();
                if (currentPageCount == 0) {
                    logger.info("当前页没有数据，导入完成");
                    hasMoreData = false;
                    break;
                }

                // 解析并保存CVE数据
                List<CveInfo> cveList = new ArrayList<>();
                for (JsonNode vulnNode : vulnerabilitiesNode) {
                    JsonNode cveNode = vulnNode.get("cve");
                    if (cveNode != null) {
                        try {
                            CveInfo cveInfo = parseCveNode(cveNode);
                            if (cveInfo != null) {
                                cveList.add(cveInfo);
                            }
                        } catch (Exception e) {
                            logger.warn("解析CVE记录失败: {}", e.getMessage());
                        }
                    }
                }

                // 批量保存到数据库
                int savedCount = saveCveList(cveList);
                totalImported += savedCount;
                
                logger.info("当前页处理完成，获取 {} 条，保存 {} 条，累计导入 {} 条", 
                           currentPageCount, savedCount, totalImported);

                // 检查是否还有更多数据
                JsonNode totalResultsNode = rootNode.get("totalResults");
                if (totalResultsNode != null) {
                    int totalResults = totalResultsNode.asInt();
                    if (startIndex + currentPageCount >= totalResults) {
                        hasMoreData = false;
                        logger.info("已获取所有数据，总计 {} 条", totalResults);
                    }
                }

                // 如果当前页数据少于每页限制，说明已经是最后一页
                if (currentPageCount < resultsPerPage) {
                    hasMoreData = false;
                    logger.info("已到达最后一页");
                }

                startIndex += resultsPerPage;

                // 添加延迟以避免API限制
                try {
                    Thread.sleep(2000); // 2秒延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            logger.info("CVE数据导入完成，总计导入 {} 条记录", totalImported);

        } catch (Exception e) {
            logger.error("导入CVE数据时发生错误", e);
        }
    }

    /**
     * 构建API请求URL
     */
    private String buildApiUrl(LocalDate startDate, LocalDate endDate, int startIndex, int resultsPerPage) {
        StringBuilder url = new StringBuilder(cveApiProperties.getBaseUrl());
        url.append("?resultsPerPage=").append(resultsPerPage);
        url.append("&startIndex=").append(startIndex);
        
        // 添加日期过滤
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        String pubStartDate = startDate.atStartOfDay().format(formatter);
        String pubEndDate = endDate.atTime(23, 59, 59).format(formatter);
        
        url.append("&pubStartDate=").append(pubStartDate);
        url.append("&pubEndDate=").append(pubEndDate);

        // 如果有API密钥，添加到URL中
        if (cveApiProperties.getApiKey() != null && !cveApiProperties.getApiKey().isEmpty()) {
            url.append("&apiKey=").append(cveApiProperties.getApiKey());
        }

        return url.toString();
    }

    /**
     * 解析CVE节点数据
     */
    private CveInfo parseCveNode(JsonNode cveNode) {
        try {
            CveInfo cveInfo = new CveInfo();

            // CVE ID
            JsonNode idNode = cveNode.get("id");
            if (idNode != null) {
                cveInfo.setCveId(idNode.asText());
            } else {
                return null; // CVE ID是必需的
            }

            // 描述
            JsonNode descriptionsNode = cveNode.get("descriptions");
            if (descriptionsNode != null && descriptionsNode.isArray()) {
                for (JsonNode descNode : descriptionsNode) {
                    JsonNode langNode = descNode.get("lang");
                    if (langNode != null && "en".equals(langNode.asText())) {
                        JsonNode valueNode = descNode.get("value");
                        if (valueNode != null) {
                            cveInfo.setDescription(valueNode.asText());
                            break;
                        }
                    }
                }
            }

            // 发布时间
            JsonNode publishedNode = cveNode.get("published");
            if (publishedNode != null) {
                String publishedStr = publishedNode.asText();
                try {
                    LocalDateTime publishedTime = LocalDateTime.parse(publishedStr.substring(0, 19));
                    cveInfo.setPublishedDate(java.sql.Timestamp.valueOf(publishedTime));
                } catch (Exception e) {
                    logger.warn("解析发布时间失败: {}", publishedStr);
                }
            }

            // 最后修改时间
            JsonNode lastModifiedNode = cveNode.get("lastModified");
            if (lastModifiedNode != null) {
                String lastModifiedStr = lastModifiedNode.asText();
                try {
                    LocalDateTime lastModifiedTime = LocalDateTime.parse(lastModifiedStr.substring(0, 19));
                    cveInfo.setUpdateDate(java.sql.Timestamp.valueOf(lastModifiedTime));
                } catch (Exception e) {
                    logger.warn("解析最后修改时间失败: {}", lastModifiedStr);
                }
            }

            // CVSS评分和严重程度
            extractCvssAndSeverity(cveNode, cveInfo);

            // 参考链接
            JsonNode referencesNode = cveNode.get("references");
            if (referencesNode != null && referencesNode.isArray()) {
                StringBuilder references = new StringBuilder();
                for (JsonNode refNode : referencesNode) {
                    JsonNode urlNode = refNode.get("url");
                    if (urlNode != null) {
                        if (references.length() > 0) {
                            references.append("\n");
                        }
                        references.append(urlNode.asText());
                    }
                }
                cveInfo.setReferences(references.toString());
            }

            // 设置默认状态
            cveInfo.setStatus("0");

            return cveInfo;

        } catch (Exception e) {
            logger.error("解析CVE节点时发生错误", e);
            return null;
        }
    }

    /**
     * 提取CVSS评分和严重程度
     */
    private void extractCvssAndSeverity(JsonNode cveNode, CveInfo cveInfo) {
        JsonNode metricsNode = cveNode.get("metrics");
        if (metricsNode == null) {
            cveInfo.setSeverityLevel("1"); // 默认低危
            return;
        }

        // 优先使用CVSS v3.1，然后v3.0，最后v2.0
        String[] cvssVersions = {"cvssMetricV31", "cvssMetricV30", "cvssMetricV2"};

        for (String version : cvssVersions) {
            JsonNode cvssMetrics = metricsNode.get(version);
            if (cvssMetrics != null && cvssMetrics.isArray() && cvssMetrics.size() > 0) {
                JsonNode firstMetric = cvssMetrics.get(0);
                JsonNode cvssData = firstMetric.get("cvssData");
                if (cvssData != null) {
                    JsonNode baseScoreNode = cvssData.get("baseScore");
                    if (baseScoreNode != null) {
                        cveInfo.setCvssScore(new BigDecimal(baseScoreNode.asDouble()));
                    }

                    JsonNode baseSeverityNode = firstMetric.get("baseSeverity");
                    if (baseSeverityNode != null) {
                        String severity = baseSeverityNode.asText().toUpperCase();
                        cveInfo.setSeverityLevel(convertSeverityToCode(severity));
                    }
                    return;
                }
            }
        }

        cveInfo.setSeverityLevel("1"); // 默认低危
    }

    /**
     * 将NVD API的严重程度文本转换为数据库编码
     */
    private String convertSeverityToCode(String severity) {
        switch (severity) {
            case "LOW":
                return "1";
            case "MEDIUM":
                return "2";
            case "HIGH":
                return "3";
            case "CRITICAL":
                return "4";
            default:
                return "1";
        }
    }

    /**
     * 批量保存CVE列表到数据库
     */
    private int saveCveList(List<CveInfo> cveList) {
        int savedCount = 0;
        for (CveInfo cveInfo : cveList) {
            try {
                // 检查是否已存在
                CveInfo existing = cveInfoMapper.selectCveInfoByCveId(cveInfo.getCveId());
                if (existing == null) {
                    cveInfoMapper.insertCveInfo(cveInfo);
                    savedCount++;
                    logger.debug("保存新CVE记录: {}", cveInfo.getCveId());
                } else {
                    // 更新现有记录
                    cveInfo.setCreateBy(existing.getCreateBy());
                    cveInfo.setCreateTime(existing.getCreateTime());
                    cveInfoMapper.updateCveInfo(cveInfo);
                    logger.debug("更新CVE记录: {}", cveInfo.getCveId());
                }
            } catch (Exception e) {
                logger.error("保存CVE记录失败: {}, 错误: {}", cveInfo.getCveId(), e.getMessage());
            }
        }
        return savedCount;
    }
}
