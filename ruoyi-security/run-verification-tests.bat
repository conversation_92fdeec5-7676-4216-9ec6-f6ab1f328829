@echo off
echo ========================================
echo 安全扫描管理系统功能验证测试
echo ========================================
echo.

:: 设置环境变量
set JAVA_OPTS=-Xmx1024m -Xms512m
set MAVEN_OPTS=-Dmaven.test.failure.ignore=false

:: 检查Java环境
echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 8或更高版本
    pause
    exit /b 1
)

:: 检查Maven环境
echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请确保已安装Maven 3.6或更高版本
    pause
    exit /b 1
)

:: 检查MySQL环境
echo 检查MySQL环境...
mysql --version
if %errorlevel% neq 0 (
    echo 错误: 未找到MySQL环境，请确保已安装MySQL 5.7或更高版本
    echo 请参考 src/test/resources/README-TEST-DATABASE.md 进行数据库配置
    pause
    exit /b 1
)

:: 检查测试数据库连接
echo 检查测试数据库连接...
mysql -u root -p123456 -e "USE ry_test; SELECT 'Database connection successful' as status;" 2>nul
if %errorlevel% neq 0 (
    echo 警告: 无法连接到测试数据库 ry_test
    echo 正在尝试创建测试数据库...

    :: 尝试创建数据库
    mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS ry_test DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;" 2>nul
    if %errorlevel% neq 0 (
        echo 错误: 无法创建测试数据库，请检查MySQL连接配置
        echo 请参考 src/test/resources/README-TEST-DATABASE.md 进行数据库配置
        pause
        exit /b 1
    )

    :: 初始化数据库表结构
    echo 初始化数据库表结构...
    mysql -u root -p123456 ry_test < src/test/resources/sql/init-test-database.sql
    if %errorlevel% neq 0 (
        echo 错误: 数据库初始化失败
        pause
        exit /b 1
    )
    echo 数据库初始化成功
) else (
    echo 数据库连接正常
)

echo.
echo ========================================
echo 开始执行验证测试
echo ========================================

:: 清理之前的测试结果
echo 清理之前的测试结果...
if exist target\surefire-reports rmdir /s /q target\surefire-reports
if exist target\site rmdir /s /q target\site

:: 编译项目
echo.
echo 1. 编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)
echo 项目编译成功

:: 执行数据库功能验证
echo.
echo 2. 执行数据库功能验证...
call mvn test -Dtest=DatabaseVerificationTest -Dspring.profiles.active=test
if %errorlevel% neq 0 (
    echo 警告: 数据库功能验证存在失败的测试用例
) else (
    echo 数据库功能验证通过
)

:: 执行项目管理功能验证
echo.
echo 3. 执行项目管理功能验证...
call mvn test -Dtest=ProjectInfoServiceTest -Dspring.profiles.active=test
if %errorlevel% neq 0 (
    echo 警告: 项目管理功能验证存在失败的测试用例
) else (
    echo 项目管理功能验证通过
)

:: 执行扫描引擎功能验证
echo.
echo 4. 执行扫描引擎功能验证...
call mvn test -Dtest=ScannerComponentTest -Dspring.profiles.active=test
if %errorlevel% neq 0 (
    echo 警告: 扫描引擎功能验证存在失败的测试用例
) else (
    echo 扫描引擎功能验证通过
)

:: 执行API接口功能验证
echo.
echo 5. 执行API接口功能验证...
call mvn test -Dtest=ProjectInfoControllerTest -Dspring.profiles.active=test
if %errorlevel% neq 0 (
    echo 警告: API接口功能验证存在失败的测试用例
) else (
    echo API接口功能验证通过
)

:: 执行系统集成验证
echo.
echo 6. 执行系统集成验证...
call mvn test -Dtest=SecurityScanIntegrationTest -Dspring.profiles.active=test
if %errorlevel% neq 0 (
    echo 警告: 系统集成验证存在失败的测试用例
) else (
    echo 系统集成验证通过
)

:: 执行完整测试套件
echo.
echo 7. 执行完整测试套件...
call mvn test -Dspring.profiles.active=test
if %errorlevel% neq 0 (
    echo 警告: 完整测试套件存在失败的测试用例
) else (
    echo 完整测试套件验证通过
)

:: 生成测试报告
echo.
echo 8. 生成测试报告...
call mvn surefire-report:report -q
if %errorlevel% neq 0 (
    echo 警告: 测试报告生成失败
) else (
    echo 测试报告生成成功
)

:: 生成代码覆盖率报告
echo.
echo 9. 生成代码覆盖率报告...
call mvn jacoco:report -q
if %errorlevel% neq 0 (
    echo 警告: 代码覆盖率报告生成失败
) else (
    echo 代码覆盖率报告生成成功
)

echo.
echo ========================================
echo 验证测试执行完成
echo ========================================

:: 显示测试结果摘要
echo.
echo 测试结果摘要:
if exist target\surefire-reports (
    echo - 测试报告目录: target\surefire-reports
    for /f %%i in ('dir /b target\surefire-reports\TEST-*.xml 2^>nul ^| find /c /v ""') do echo - 测试类数量: %%i
)

if exist target\site\jacoco (
    echo - 代码覆盖率报告: target\site\jacoco\index.html
)

if exist target\site\surefire-report.html (
    echo - 测试报告: target\site\surefire-report.html
)

echo.
echo 验证建议:
echo 1. 查看详细的测试报告了解具体的测试结果
echo 2. 检查代码覆盖率是否达到80%%以上的目标
echo 3. 对于失败的测试用例，请检查具体的错误信息
echo 4. 确保所有核心功能都有对应的测试用例覆盖

echo.
echo 如需查看详细报告，请打开以下文件:
if exist target\site\surefire-report.html (
    echo - 测试报告: target\site\surefire-report.html
)
if exist target\site\jacoco\index.html (
    echo - 覆盖率报告: target\site\jacoco\index.html
)

echo.
pause
