<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('项目管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="project-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                项目名称：<input type="text" name="projectName"/>
                            </li>
                            <li>
                                Git地址：<input type="text" name="gitUrl"/>
                            </li>
                            <li>
                                状态：<select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="security:project:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="security:project:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="security:project:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info single disabled" onclick="testConnection()" shiro:hasPermission="security:project:test">
                    <i class="fa fa-plug"></i> 测试连接
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="security:project:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('security:project:edit')}]];
        var removeFlag = [[${@permission.hasPermi('security:project:remove')}]];
        var testFlag = [[${@permission.hasPermi('security:project:test')}]];
        var statusDatas = [[${@dict.getType('sys_normal_disable')}]];
        var prefix = ctx + "security/project";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'projectId',
                    title: '项目ID',
                    visible: false
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'projectCode',
                    title: '项目编码'
                },
                {
                    field: 'gitUrl',
                    title: 'Git地址',
                    formatter: function(value, row, index) {
                        if (value && value.length > 50) {
                            return '<span title="' + value + '">' + value.substring(0, 50) + '...</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'gitBranch',
                    title: '分支'
                },
                {
                    field: 'projectDesc',
                    title: '描述',
                    formatter: function(value, row, index) {
                        if (value && value.length > 30) {
                            return '<span title="' + value + '">' + value.substring(0, 30) + '...</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'scanStatus',
                    title: '扫描状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '0') return '<span class="label label-default">未扫描</span>';
                        if (value == '1') return '<span class="label label-warning">扫描中</span>';
                        if (value == '2') return '<span class="label label-success">已完成</span>';
                        if (value == '3') return '<span class="label label-danger">扫描失败</span>';
                        return '<span class="label label-default">未知</span>';
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.projectId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-info btn-xs ' + testFlag + '" href="javascript:void(0)" onclick="testConnection(\'' + row.projectId + '\')"><i class="fa fa-plug"></i>测试</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.projectId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function testConnection(projectId) {
            var url = prefix + "/testConnection";
            var data = {};
            if (projectId) {
                data.projectId = projectId;
            } else {
                var rows = $.table.selectFirstColumns();
                if (rows.length == 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                data.projectId = rows[0];
            }
            
            $.modal.loading("正在测试连接，请稍候...");
            $.ajax({
                url: url,
                type: 'post',
                data: data,
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess("连接测试成功");
                    } else {
                        $.modal.alertError("连接测试失败：" + result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("连接测试失败");
                }
            });
        }
    </script>
</body>
</html>
