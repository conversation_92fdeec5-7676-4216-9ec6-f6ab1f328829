<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('扫描管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="scan-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                项目名称：<input type="text" name="projectName"/>
                            </li>
                            <li>
                                扫描状态：<select name="scanStatus">
                                    <option value="">所有</option>
                                    <option value="PENDING">待扫描</option>
                                    <option value="RUNNING">扫描中</option>
                                    <option value="COMPLETED">已完成</option>
                                    <option value="FAILED">失败</option>
                                </select>
                            </li>
                            <li class="select-time">
                                <label>扫描时间： </label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="startScan()" shiro:hasPermission="security:scan:start">
                    <i class="fa fa-play"></i> 开始扫描
                </a>
                <a class="btn btn-danger single disabled" onclick="stopScan()" shiro:hasPermission="security:scan:stop">
                    <i class="fa fa-stop"></i> 停止扫描
                </a>
                <a class="btn btn-info single disabled" onclick="viewReport()" shiro:hasPermission="security:scan:report">
                    <i class="fa fa-file-text-o"></i> 查看报告
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="security:scan:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var startFlag = [[${@permission.hasPermi('security:scan:start')}]];
        var stopFlag = [[${@permission.hasPermi('security:scan:stop')}]];
        var reportFlag = [[${@permission.hasPermi('security:scan:report')}]];
        var prefix = ctx + "security/scan";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "扫描任务",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'taskId',
                    title: '扫描ID',
                    visible: false
                },
                {
                    field: 'projectId',
                    title: '项目ID',
                    visible: false
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'taskStatus',
                    title: '扫描状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var statusMap = {
                            '0': '<span class="label label-warning">待执行</span>',
                            '1': '<span class="label label-info">执行中</span>',
                            '2': '<span class="label label-success">已完成</span>',
                            '3': '<span class="label label-danger">执行失败</span>'
                        };
                        return statusMap[value] || value;
                    }
                },
                {
                    field: 'progress',
                    title: '进度',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (row.taskStatus === '1') {
                            return '<div class="progress progress-mini"><div class="progress-bar" style="width: ' + (value || 0) + '%"></div></div>' + (value || 0) + '%';
                        }
                        return row.taskStatus === '2' ? '100%' : '-';
                    }
                },
                {
                    field: 'totalDependencies',
                    title: '依赖总数',
                    align: 'center'
                },
                {
                    field: 'cveCount',
                    title: 'CVE数量',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value > 0) {
                            return '<span class="label label-danger">' + value + '</span>';
                        }
                        return value || 0;
                    }
                },
                {
                    field: 'highRiskLicenseCount',
                    title: '高风险许可证',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value > 0) {
                            return '<span class="label label-warning">' + value + '</span>';
                        }
                        return value || 0;
                    }
                },
                {
                    field: 'startTime',
                    title: '扫描时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (row.taskStatus === '0') {
                            actions.push('<a class="btn btn-success btn-xs ' + startFlag + '" href="javascript:void(0)" onclick="startScan(\'' + row.projectId + '\')"><i class="fa fa-play"></i>开始</a> ');
                        }
                        if (row.taskStatus === '1') {
                            actions.push('<a class="btn btn-danger btn-xs ' + stopFlag + '" href="javascript:void(0)" onclick="stopScan(\'' + row.projectId + '\')"><i class="fa fa-stop"></i>停止</a> ');
                        }
                        if (row.taskStatus === '2') {
                            actions.push('<a class="btn btn-info btn-xs ' + reportFlag + '" href="javascript:void(0)" onclick="viewReport(\'' + row.projectId + '\')"><i class="fa fa-file-text-o"></i>报告</a> ');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function startScan(projectId) {
            if (!projectId) {
                // 显示项目选择对话框
                showProjectSelectDialog();
                return;
            }

            $.modal.confirm("确认要开始扫描吗？", function() {
                $.modal.loading("正在启动扫描，请稍候...");
                $.ajax({
                    url: prefix + "/start",
                    type: 'post',
                    data: { scanId: projectId }, // 后端仍然使用scanId参数名，但传递的是projectId
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess("扫描已启动");
                            $.table.refresh();
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            });
        }

        function stopScan(projectId) {
            var rows = projectId ? [projectId] : $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            $.modal.confirm("确认要停止扫描吗？", function() {
                $.ajax({
                    url: prefix + "/stop",
                    type: 'post',
                    data: { scanId: rows[0] }, // 后端仍然使用scanId参数名，但传递的是projectId
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess("扫描已停止");
                            $.table.refresh();
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            });
        }

        function viewReport(projectId) {
            var rows = projectId ? [projectId] : $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            var url = ctx + "security/report/detail/" + rows[0];
            $.modal.openTab("扫描报告", url);
        }

        function showProjectSelectDialog() {
            var url = prefix + "/selectProject";
            $.modal.open("选择项目", url, '800', '600');
        }
    </script>
</body>
</html>
