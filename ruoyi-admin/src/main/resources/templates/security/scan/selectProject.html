<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择项目')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="project-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                项目名称：<input type="text" name="projectName"/>
                            </li>
                            <li>
                                状态：<select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 text-center">
                <button type="button" class="btn btn-success" onclick="selectProject()">
                    <i class="fa fa-check"></i> 确定选择
                </button>
                <button type="button" class="btn btn-default" onclick="$.modal.close()">
                    <i class="fa fa-close"></i> 取消
                </button>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "security/project";

        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "项目",
                singleSelect: true,
                columns: [{
                    radio: true
                },
                {
                    field: 'projectId',
                    title: '项目ID',
                    visible: false
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'gitUrl',
                    title: 'Git地址',
                    formatter: function(value, row, index) {
                        if (value && value.length > 50) {
                            return value.substring(0, 50) + '...';
                        }
                        return value;
                    }
                },
                {
                    field: 'gitBranch',
                    title: '分支'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel([[${@dict.getType('sys_normal_disable')}]], value);
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                }]
            };
            $.table.init(options);
        });

        function selectProject() {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请选择一个项目");
                return;
            }
            
            var projectId = rows[0];
            var projectName = $('#bootstrap-table').bootstrapTable('getSelections')[0].projectName;
            
            // 创建扫描任务
            $.modal.loading("正在创建扫描任务，请稍候...");
            $.ajax({
                url: ctx + "security/scan/create",
                type: 'post',
                data: { 
                    projectId: projectId,
                    projectName: projectName
                },
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess("扫描任务创建成功");
                        $.modal.close();
                        // 刷新父页面表格
                        if (parent.$.table) {
                            parent.$.table.refresh();
                        }
                    } else {
                        $.modal.alertError("创建扫描任务失败：" + result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("创建扫描任务失败");
                }
            });
        }
    </script>
</body>
</html>
