<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('CVE管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="cve-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                CVE编号：<input type="text" name="cveId"/>
                            </li>
                            <li>
                                严重程度：<select name="severity">
                                    <option value="">所有</option>
                                    <option value="CRITICAL">严重</option>
                                    <option value="HIGH">高</option>
                                    <option value="MEDIUM">中</option>
                                    <option value="LOW">低</option>
                                </select>
                            </li>
                            <li>
                                影响组件：<input type="text" name="component"/>
                            </li>
                            <li class="select-time">
                                <label>发布时间： </label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="showSyncDialog()" shiro:hasPermission="security:cve:sync">
                    <i class="fa fa-refresh"></i> 同步CVE数据
                </a>
                <a class="btn btn-info single disabled" onclick="viewDetail()" shiro:hasPermission="security:cve:list">
                    <i class="fa fa-eye"></i> 查看详情
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="security:cve:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var syncFlag = [[${@permission.hasPermi('security:cve:sync')}]];
        var detailFlag = [[${@permission.hasPermi('security:cve:list')}]];
        var exportFlag = [[${@permission.hasPermi('security:cve:export')}]];
        var prefix = ctx + "security/cve";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "CVE漏洞",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'cveId',
                    title: 'CVE编号',
                    formatter: function(value, row, index) {
                        return '<a href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=' + value + '" target="_blank">' + value + '</a>';
                    }
                },
                {
                    field: 'severity',
                    title: '严重程度',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var severityMap = {
                            'CRITICAL': '<span class="label label-danger">严重</span>',
                            'HIGH': '<span class="label label-warning">高</span>',
                            'MEDIUM': '<span class="label label-info">中</span>',
                            'LOW': '<span class="label label-success">低</span>'
                        };
                        return severityMap[value] || '<span class="label label-default">' + value + '</span>';
                    }
                },
                {
                    field: 'cvssScore',
                    title: 'CVSS评分',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value >= 9.0) {
                            return '<span class="text-danger"><strong>' + value + '</strong></span>';
                        } else if (value >= 7.0) {
                            return '<span class="text-warning"><strong>' + value + '</strong></span>';
                        } else if (value >= 4.0) {
                            return '<span class="text-info">' + value + '</span>';
                        } else {
                            return '<span class="text-success">' + value + '</span>';
                        }
                    }
                },
                {
                    field: 'component',
                    title: '影响组件'
                },
                {
                    field: 'description',
                    title: '漏洞描述',
                    formatter: function(value, row, index) {
                        if (value && value.length > 100) {
                            return value.substring(0, 100) + '...';
                        }
                        return value;
                    }
                },
                {
                    field: 'publishedDate',
                    title: '发布时间',
                    sortable: true
                },
                {
                    field: 'lastModifiedDate',
                    title: '最后更新',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="viewDetail(\'' + row.cveId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        actions.push('<a class="btn btn-primary btn-xs" href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=' + row.cveId + '" target="_blank"><i class="fa fa-external-link"></i>官方</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function showSyncDialog() {
            // 首先检查API配置状态
            $.ajax({
                url: ctx + "security/cve/sync/config",
                type: 'get',
                success: function(result) {
                    if (result.code == web_status.SUCCESS && result.configured) {
                        // API配置正常，显示同步确认对话框
                        $.modal.confirm("确认要同步最近30天的CVE数据吗？此操作可能需要较长时间。", function() {
                            startCveSync();
                        });
                    } else {
                        // API配置异常
                        $.modal.alertError("CVE API配置异常：" + (result.message || result.msg || "请检查配置文件中的API Key设置"));
                    }
                },
                error: function() {
                    $.modal.alertError("检查API配置失败，请稍后重试");
                }
            });
        }

        function startCveSync() {
            $.modal.loading("正在启动CVE数据同步，请稍候...");
            $.ajax({
                url: ctx + "security/cve/sync/start",
                type: 'post',
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                        // 开始轮询同步状态
                        checkSyncStatus();
                    } else {
                        $.modal.alertError("启动CVE数据同步失败：" + result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("启动CVE数据同步失败，请检查网络连接");
                }
            });
        }

        function checkSyncStatus() {
            var statusInterval = setInterval(function() {
                $.ajax({
                    url: ctx + "security/cve/sync/status",
                    type: 'get',
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            if (result.syncing) {
                                // 同步进行中，显示进度
                                console.log("同步进度: " + result.progress + "% - " + result.message);
                            } else {
                                // 同步完成
                                clearInterval(statusInterval);
                                $.modal.alertSuccess("CVE数据同步完成：" + result.message);
                                $.table.refresh();
                            }
                        }
                    }
                });
            }, 3000); // 每3秒检查一次状态
        }

        function viewDetail(cveId) {
            var rows = cveId ? [cveId] : $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            
            var url = prefix + "/detail/" + rows[0];
            $.modal.open("CVE详情", url, '900', '700');
        }
    </script>
</body>
</html>
