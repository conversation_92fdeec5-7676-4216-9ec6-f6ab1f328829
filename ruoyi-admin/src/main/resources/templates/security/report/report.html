<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('扫描报告')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="report-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                项目名称：<input type="text" name="projectName"/>
                            </li>
                            <li>
                                报告标题：<input type="text" name="reportTitle"/>
                            </li>
                            <li class="select-time">
                                <label>生成时间： </label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-info single disabled" onclick="viewDetail()" shiro:hasPermission="security:report:detail">
                    <i class="fa fa-eye"></i> 查看详情
                </a>
                <a class="btn btn-success single disabled" onclick="downloadReport()" shiro:hasPermission="security:report:export">
                    <i class="fa fa-download"></i> 下载报告
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="security:report:export">
                    <i class="fa fa-file-excel-o"></i> 导出Excel
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var detailFlag = [[${@permission.hasPermi('security:report:detail')}]];
        var exportFlag = [[${@permission.hasPermi('security:report:export')}]];
        var prefix = ctx + "security/report";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "扫描报告",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'reportId',
                    title: '报告ID',
                    visible: false
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'reportTitle',
                    title: '报告标题'
                },
                {
                    field: 'totalDependencies',
                    title: '依赖总数',
                    align: 'center'
                },
                {
                    field: 'cveCount',
                    title: 'CVE漏洞',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value > 0) {
                            return '<span class="label label-danger">' + value + '</span>';
                        }
                        return '<span class="label label-success">0</span>';
                    }
                },
                {
                    field: 'highRiskLicenseCount',
                    title: '高风险许可证',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value > 0) {
                            return '<span class="label label-warning">' + value + '</span>';
                        }
                        return '<span class="label label-success">0</span>';
                    }
                },
                {
                    field: 'riskLevel',
                    title: '风险等级',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var riskMap = {
                            'HIGH': '<span class="label label-danger">高风险</span>',
                            'MEDIUM': '<span class="label label-warning">中风险</span>',
                            'LOW': '<span class="label label-info">低风险</span>',
                            'SAFE': '<span class="label label-success">安全</span>'
                        };
                        return riskMap[value] || '<span class="label label-default">未知</span>';
                    }
                },
                {
                    field: 'generateTime',
                    title: '生成时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="viewDetail(\'' + row.reportId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + exportFlag + '" href="javascript:void(0)" onclick="downloadReport(\'' + row.reportId + '\')"><i class="fa fa-download"></i>下载</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function viewDetail(reportId) {
            var rows = reportId ? [reportId] : $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            
            var url = prefix + "/detail/" + rows[0];
            $.modal.openTab("报告详情", url);
        }

        function downloadReport(reportId) {
            var rows = reportId ? [reportId] : $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            
            $.modal.loading("正在生成报告，请稍候...");
            window.location.href = prefix + "/download/" + rows[0];
            setTimeout(function() {
                $.modal.closeLoading();
            }, 2000);
        }
    </script>
</body>
</html>
